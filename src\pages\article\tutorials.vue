<template>
  <div class="page-wrapper">
    <search-cate-card></search-cate-card>
    <div class="w-[1280px] px-[80px] mx-auto pt-[56px] pb-[128px]">
      <div class="text-[52px] leading-[52px] font-medium text-center">
        {{ authStore.i18n("cm_news.chilatshopTutorials") }}
      </div>
      <div class="mt-[58px]">
        <n-grid cols="4" x-gap="22" y-gap="24" responsive="screen">
          <n-grid-item v-for="video in videoData" :key="video.id">
            <div @click="onOpenVideo(video)">
              <div class="relative">
                <img
                  loading="lazy"
                  :src="videoPlay"
                  alt="video"
                  class="w-[50px] h-[48px] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
                />
                <n-image
                  lazy
                  preview-disabled
                  :src="video.poster"
                  class="w-full rounded-[4px]"
                />
              </div>

              <div class="text-[14px] leading-[20px] mt-[12px]">
                {{ video.title }}
              </div>
            </div>
          </n-grid-item>
        </n-grid>
      </div>
    </div>
    <video-modal ref="videoModalRef"></video-modal>
  </div>
</template>

<script setup lang="ts">
import videoPlay from "@/assets/icons/open/videoPlay.svg";
import { useAuthStore } from "@/stores/authStore";
import videoBg1 from "@/assets/icons/article/videoBg1.png";
import videoBg2 from "@/assets/icons/article/videoBg2.png";
import videoBg3 from "@/assets/icons/article/videoBg3.png";
import videoBg4 from "@/assets/icons/article/videoBg4.png";
import videoBg5 from "@/assets/icons/article/videoBg5.png";
import videoBg6 from "@/assets/icons/article/videoBg6.png";
import videoBg7 from "@/assets/icons/article/videoBg7.jpg";
import videoBg8 from "@/assets/icons/article/videoBg8.jpg";
import videoBg9 from "@/assets/icons/article/videoBg9.jpg";
import videoBg10 from "@/assets/icons/article/videoBg10.jpg";
import videoBg11 from "@/assets/icons/article/videoBg11.jpg";
import videoBg12 from "@/assets/icons/article/videoBg12.png";
import videoBg13 from "@/assets/icons/article/videoBg13.jpg";
import videoBg14 from "@/assets/icons/article/videoBg14.png";
import videoBg15 from "@/assets/icons/article/videoBg15.jpg";
import videoBg16 from "@/assets/icons/article/videoBg16.jpg";
import videoBg17 from "@/assets/icons/article/videoBg17.jpg";
import videoBg18 from "@/assets/icons/article/videoBg18.jpg";
import videoBg19 from "@/assets/icons/article/videoBg19.jpg";
import useCoupon from "@/assets/icons/article/use-coupon.jpg";
import inviteFriends from "@/assets/icons/article/invite-friends.jpg";
import mobileInviteFriends from "@/assets/icons/article/mobile-invite-friends.jpg";
import customSearch from "@/assets/icons/article/custom-search.jpg";
import mobileCustomSearch from "@/assets/icons/article/mobile-custom-search.jpg";
import inviteLink from "@/assets/icons/article/invite-link.jpg";
import mobileInviteLink from "@/assets/icons/article/mobile-invite-link.jpg";

const authStore = useAuthStore();
const videoModalRef = ref<any>(null);

// 设置SEO规范链接
setSeoCanonical();

// 设置SEO
useHead({
  title: `${authStore.i18n("cm_news.chilatshopTutorials")}-ChilatShop`,
});

const videoData = [
  {
    id: "TrHD4-Slok0",
    poster: videoBg1,
    title: authStore.i18n("cm_common.selectProducts"),
    titleCh: "1.如何选品",
  },
  {
    id: "eSGovgiFse0",
    poster: videoBg2,
    title: authStore.i18n("cm_common.placeOrder"),
    titleCh: "2.选完品后如何提交订单教程--电脑版",
  },
  {
    id: "RpqMaqjgffA",
    poster: videoBg3,
    title: authStore.i18n("cm_common.placeOrderMobile"),
    titleCh: "3.选完品后如何提交订单教程--手机版",
  },
  {
    id: "-5ZBcr0rgJs",
    poster: videoBg4,
    title: authStore.i18n("cm_common.makePayment"),
    titleCh: "4.在我们网站如何付款",
  },
  {
    id: "c19bwnIn_nA",
    poster: videoBg5,
    title: authStore.i18n("cm_common.makePaymentPeru"),
    titleCh: "5.付款教程 秘鲁",
  },
  {
    id: "DKGNt3JZCtc",
    poster: videoBg6,
    title: authStore.i18n("cm_common.makePaymentMexico"),
    titleCh: "6.付款教程 墨西哥",
    url: "https://www.youtube.com/watch?v=DKGNt3JZCtc",
  },
  {
    id: "wlWMu4fyXNQ",
    poster: videoBg7,
    title: authStore.i18n("cm_common.searchByImage"),
    titleCh: "7.以图搜图教程--电脑版",
  },
  {
    id: "_8kkzfCuc6k",
    poster: videoBg8,
    title: authStore.i18n("cm_common.searchByImageMobile"),
    titleCh: "8.以图搜图教程--手机版",
  },
  {
    id: "_sXxQ6SR3Ao",
    poster: videoBg9,
    title: authStore.i18n("cm_common.registerClaimBonus"),
    titleCh: "9.如何注册&领取注册新礼包--电脑版",
  },
  {
    id: "KUPNRaNE_fU",
    poster: videoBg10,
    title: authStore.i18n("cm_common.registerClaimBonusMobile"),
    titleCh: "10.如何注册&领取注册新礼包--手机版",
  },
  {
    id: "FVQy8vIjP-c",
    poster: videoBg11,
    title: authStore.i18n("cm_common.supportHelp"),
    titleCh: "11.如何联系客服&阅览常见问题--电脑版",
  },
  {
    id: "f-lRY5CQgzU",
    poster: videoBg12,
    title: authStore.i18n("cm_common.supportHelpMobile"),
    titleCh: "12.如何联系客服&阅览常见问题--手机版",
  },
  {
    id: "hQwy27h_cQ4",
    poster: videoBg13,
    title: authStore.i18n("cm_tutorials.editCart"),
    titleCh: "13.如何编辑购物车？--电脑版",
  },
  {
    id: "ebN8EJ0o74M",
    poster: videoBg14,
    title: authStore.i18n("cm_tutorials.editCart"),
    titleCh: "14.如何编辑购物车？--手机版",
  },
  {
    id: "88H3NKcqEIc",
    poster: videoBg15,
    title: authStore.i18n("cm_tutorials.checkOrder"),
    titleCh: "15.如何查看订单状态？--电脑版",
  },
  {
    id: "p0epvtYGe4s",
    poster: videoBg16,
    title: authStore.i18n("cm_tutorials.checkOrder"),
    titleCh: "16.如何查看订单状态？--手机版",
  },
  {
    id: "HdzVbDRLYSA",
    poster: videoBg17,
    title: authStore.i18n("cm_tutorials.forgotPassword"),
    titleCh: "17.你忘记了密码，无法登录？",
    isVertical: true, // 竖屏视频
  },
  {
    id: "CE7mvGFzWp4",
    poster: videoBg18,
    title: authStore.i18n("cm_tutorials.isPriceInDollars"),
    titleCh: "18.商品页面的价格是美元吗？",
    isVertical: true, // 竖屏视频
  },
  {
    id: "Ipn4jhCNDzg",
    poster: videoBg19,
    title: authStore.i18n("cm_tutorials.includesTaxAndShipping"),
    titleCh: "19.你忘记了密码，无法登录？",
    isVertical: true, // 竖屏视频
  },
  {
    id: "W7G9E2ShuCk",
    poster: useCoupon,
    title: "¿Cómo usar tu cupón al pagar?",
    titleCh: "如何使用优惠券付款PC版？",
  },
  {
    id: "y9YL0Wa1wEc",
    poster: inviteFriends,
    title: "¿Cómo puede un usuario antiguo invitar a nuevos usuarios?",
    titleCh: "老用户如何邀请新用户？PC",
  },
  {
    id: "AiK0wKhtSeQ",
    poster: mobileInviteFriends,
    title: "¿Cómo puede un usuario antiguo invitar a nuevos usuarios?",
    titleCh: "老用户如何邀请新用户？H5",
  },
  {
    id: "ce_odIuQoXQ",
    poster: customSearch,
    title: "¿Cómo usar nuestra función de búsqueda personalizada de productos?",
    titleCh: "如何使用我们的定制产品搜索功能？PC",
  },
  {
    id: "hDQ34DVvF7U",
    poster: mobileCustomSearch,
    title: "¿Cómo usar nuestra función de búsqueda personalizada de productos?",
    titleCh: "如何使用我们的定制产品搜索功能？H5",
  },
  {
    id: "1ibyOV4IXKQ",
    poster: inviteLink,
    title: "Cómo registrarte con un enlace de invitación--PC",
    titleCh: "新客户老带新链接注册领优惠券PC",
  },
  {
    id: "dVrPTY7M6xo",
    poster: mobileInviteLink,
    title: "Cómo registrarte con un enlace de invitación--H5",
    titleCh: "新客户老带新链接注册领优惠券H5",
  },
];

function onOpenVideo(video: any) {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}
</script>

<style scoped lang="scss">
.page-wrapper {
  width: 1280px;
  margin: 0 auto;
  color: #222;
}
.page-header {
  width: 100%;
  height: 410px;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/article/inviteBg.jpg");
  background-repeat: no-repeat;
}
.page-footer {
  width: 100%;
  height: 150px;
  position: relative;
  background-size: 100%100%;
  background-image: url("@/assets/icons/article/footerBg.jpg");
  background-repeat: no-repeat;
}
</style>
