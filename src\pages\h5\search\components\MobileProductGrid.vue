<template>
  <a
    :href="pageData.linkUrl"
    @click="onLinkTo"
    :data-spm-box="props.pageSource"
    :data-spm-index="props.goodsIndex + 1"
    :data-spm-param="route?.query?.tagId || null"
  >
    <n-card
      :bordered="false"
      class="w-[3.36rem] hover:cursor-pointer text-black"
    >
      <div class="flex flex-col w-[3.36rem]">
        <n-image
          lazy
          preview-disabled
          class="w-[3.36rem] h-[3.36rem] rounded-[0.2rem]"
          :src="goods.mainImageUrl"
          :alt="goods.goodsName"
          :img-props="{ referrerpolicy: 'no-referrer' }"
          object-fit="cover"
        >
        </n-image>
        <div
          v-if="pageData.type !== 'imgSearch'"
          class="goods-name text-[0.26rem] leading-[0.32rem] mt-[0.16rem] text-[#666]"
        >
          {{ goods.goodsName }}
        </div>

        <div class="w-full mt-[0.2rem]">
          <n-ellipsis
            :line-clamp="1"
            class="text-[0.24rem] leading-[0.3rem] break-all text-[#111] font-medium"
            :tooltip="false"
          >
            <span v-if="pageData.type !== 'imgSearch'" class="text-[0.3rem]">
              <!-- 规格单价的最小值和最大值展示；若相同则只展示一个价格值 -->
              <!-- <span v-if="goods.minPrice != goods.maxPrice"
                >{{ setUnit(goods.minPrice) }} - {{ goods.maxPrice }}</span
              >
              <span v-else>{{ setUnit(goods.maxPrice) }}</span> -->
              <span>{{ setUnit(goods.minPrice) }}</span>
              <!-- <span class="text-[0.28rem] mr-[0.04rem]">{{
                monetaryUnit
              }}</span>
              <span v-if="goods.minPrice != goods.maxPrice">
                <span class="text-[0.32rem]"
                  >{{ getIntegerPart(goods.minPrice) }} </span
                >.<span class="text-[0.28rem]">{{
                  getDecimalPart(goods.minPrice)
                }}</span>
                -
                <span class="text-[0.32rem]">{{
                  getIntegerPart(goods.maxPrice)
                }}</span
                >.<span class="text-[0.28rem]">{{
                  getDecimalPart(goods.maxPrice)
                }}</span>
              </span>
              <span v-else
                ><span class="text-[0.32rem]">{{
                  getIntegerPart(goods.maxPrice)
                }}</span
                >.<span class="text-[0.28rem]">{{
                  getDecimalPart(goods.maxPrice)
                }}</span></span
              > -->
            </span>
            <!--以图搜图只展示最低价-->
            <span v-else>
              <span class="text-[0.28rem] mr-[0.04rem]">{{
                monetaryUnit
              }}</span>
              <span class="text-[0.28rem]"
                >{{ getIntegerPart(goods.minPrice) }} </span
              >.<span class="text-[0.28rem]">{{
                getDecimalPart(goods.minPrice)
              }}</span>
            </span>
          </n-ellipsis>
          <div
            v-if="goods.pcsEstimateFreight"
            class="text-[0.24rem] leading-[0.32rem] text-[#666]"
          >
            {{ authStore.i18n("cm_goods.shippingCost") }}<br />
            <span>{{ setUnit(goods.pcsEstimateFreight) }}</span>
            /
            {{ goods?.goodsPriceUnitName }}
          </div>
        </div>
      </div>
    </n-card>
  </a>
</template>

<script setup lang="ts" name="MobileProductCard">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();
const emit = defineEmits([
  "onOpenDetail",
  "onUpdateGoodsId",
  "onUpdateLoading",
]);

const props = defineProps({
  goods: {
    type: Object,
    default: () => {},
  },
  discountRate: {
    type: String,
    default: "",
  },
  pageSource: {
    type: String,
    default: "",
  },
  goodsIndex: {
    type: Number,
    default: 1,
  },
  from: {
    type: String,
    default: "search",
  },
});

const pageData = reactive(<any>{
  type: route.query.type || "",
  linkUrl: "",
});
// 获取商品详情链接
const getLinkUrl = async (): Promise<string | null> => {
  let goodsId = props.goods.goodsId;
  if (!goodsId) {
    emit("onUpdateLoading", true);
    const res: any = await useGetGoods({ str: props.goods.sourceGoodsId });
    emit("onUpdateLoading", false);

    if (res?.result?.code === 200) {
      goodsId = res?.data;
      emit("onUpdateGoodsId", props.goodsIndex, goodsId);
    } else {
      showToast(authStore.i18n("cm_common_addGoodsError"));
      return null;
    }
  }
  return `/h5/goods/${goodsId}`;
};

// 跳转详情页
const onLinkTo = async (event: any) => {
  event.preventDefault();
  const url = await getLinkUrl();
  if (!url) return;

  const params: Record<string, string> = {};
  if (props.goods.padc) params.padc = props.goods.padc;

  navigateToPage(url, params, false, event);

  // 埋点
  window?.MyStat?.addPageEvent?.(
    "click_goods_detail",
    `商品编码：${props.goods.goodsNo || "N/A"}`
  );
};

async function onAddCart(e: any) {
  emit("onOpenDetail", e, props.goodsIndex);
}

function getIntegerPart(price: any) {
  return Math.floor(price); // 获取整数部分
}

function getDecimalPart(price: any) {
  return (price % 1).toFixed(2).substring(2); // 获取小数部分
}
</script>

<style scoped lang="scss">
.n-card :deep(.n-card__content) {
  padding: 0;
}
.goods-name {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
