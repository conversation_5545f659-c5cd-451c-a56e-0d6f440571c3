<template>
  <div class="page-wrapper">
    <search-cate-card></search-cate-card>
    <div class="header_banner" :style="`background-image: url(${headerBg})`">
      <div class="cwidth mx-auto flex items-center h-full">
        <div class="header-content">
          <div class="header-title">Haga la importación más fácil y seguro</div>
          <div class="header-desc">
            Chilat shop es la plataforma líder de agente de compras
            internacional. Chilat shop es la plataforma de importación todo en
            uno donde gestionamos por ti todo el proceso de compra, logística e
            importación.
          </div>
          <n-button
            color="#db2221"
            @click="onLoginClick"
            class="header-button"
            v-if="!userInfo?.username"
          >
            {{ authStore.i18n("cm_common_registerNow") }}
          </n-button>
        </div>
      </div>
    </div>
    <div class="section-banner">
      <div class="section-wrapper">
        <n-grid cols="2">
          <n-grid-item class="flex flex-col items-center">
            <n-image
              lazy
              preview-disabled
              :src="bannerBg1"
              class="section-img"
            />
            <div class="section-img flex justify-between">
              <n-image
                lazy
                preview-disabled
                :src="bannerBg2"
                class="w-[158px]"
              />
              <n-image
                lazy
                preview-disabled
                :src="bannerBg3"
                class="w-[158px]"
              />
            </div>
            <n-image
              lazy
              preview-disabled
              :src="bannerBg4"
              class="section-img"
            />
            <n-image
              lazy
              preview-disabled
              :src="bannerBg5"
              class="section-img"
            />
          </n-grid-item>
          <n-grid-item class="pr-20"
            ><div class="section-title">
              Disfrute de una forma diferente de importar
            </div>
            <div class="section-desc">
              Chilat es un líder en servicios del idioma español en China y
              llevamos 22 años enfocado en servicios de agente de compras en
              Latinoamérica.
            </div>
            <div class="section-desc">
              Estamos comprometidos a permitir que más clientes comerciales
              compren directamente a proveedores de primera mano en China a
              través de métodos de importación más sencillos y convenientes,
              obteniendo así precios de compra más competitivos!
            </div>
            <div class="section-desc">
              Nuestra sede central se encuentra en Yiwu, China, y contamos con
              sucursales en México, Shanghai, Guangzhou y Hangzhou, con una
              superficie operativa de oficinas de más de 2000 metros cuadrados,
              una superficie de almacén de 1500 metros cuadrados para nuestro
              grupo de empresas y más de 100 empleados.
            </div>
            <div class="section-desc flex">
              <div class="number-item">
                <span>+2,000m²</span>
                <p>de oficinas</p>
              </div>
              <div class="number-item">
                <span>+1,500m²</span>
                <p>almacenes</p>
              </div>
              <div class="number-item">
                <span>+100</span>
                <p>empleados</p>
              </div>
            </div>
          </n-grid-item>
        </n-grid>
      </div>
    </div>
    <div
      class="section-banner section-bg"
      :style="`background-image: url(${bannerBg7})`"
    >
      <div class="section-wrapper">
        <div class="section-content">
          <div class="section-title">
            Nuestro negocio se extiende por toda América Latina, y nuestra
            visión es convertirnos en el líder de las marcas chinas que salen al
            exterior en América Latina.
          </div>
          <div class="section-desc">
            Nuestra misión es ayudar a las marcas chinas a liderar el comercio
            latinoamericano. Ser reconocida en Latinoamérica como la empresa
            internacional líder que genera las mejores oportunidades de negocios
            con China basándose en el respeto por el cliente, la transparencia y
            honestidad en todo acuerdo comercial, agilidad, innovación y
            profesionalismo en los servicios que ofrece y por su calidez humana
            y relaciones de confianza a largo plazo.
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const loginRegister = inject<any>("loginRegister");
const userInfo = computed(() => useAuthStore().getUserInfo);

// 设置SEO规范链接
setSeoCanonical();

const headerBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/11/66c7c51d-a279-4ae6-b9a6-85db154989b2.jpg";
const bannerBg1 =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/01/a05a58e5-593a-411f-aca7-6c21a2c24891.png";
const bannerBg2 =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/01/fe5bd1aa-b16f-4756-96a5-6cf3c1b7a4b6.png";
const bannerBg3 =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/01/7c4987b1-193e-4db8-a54c-6551ea59aa4a.png";
const bannerBg4 =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/01/eae7366c-ca87-47a4-bc69-e282b55f50a2.png";
const bannerBg5 =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/01/65a3941e-a5bb-446e-ad8f-a8ae4bfe8cbf.png";
const bannerBg6 =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/01/056a6109-90ee-44b7-bba9-f1031b4e6c26.png";
const bannerBg7 =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/01/056a6109-90ee-44b7-bba9-f1031b4e6c26.png";

// 设置SEO
useHead({
  title: `${authStore.i18n("cm_news.aboutUs")}-ChilatShop`,
});

const onLoginClick = async () => {
  window?.MyStat?.addPageEvent(
    "passport_open_nav_register",
    "点击顶部导航注册，打开注册窗口"
  ); // 埋点
  loginRegister?.openLogin("", 0);
};
</script>

<style scoped lang="scss">
.page-wrapper {
  width: 1280px;
  margin: 0 auto;
  color: #222;
}
.header_banner {
  height: 624px;
  min-width: 1280px;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 1920px 624px;

  .header-content {
    width: 580px;
    padding: 0 60px;
    margin-left: 40px;
    .header-title {
      font-size: 44px;
      line-height: 60px;
      font-weight: 500;
      margin-bottom: 20px;
    }
    .header-desc {
      font-size: 24px;
      line-height: 34px;
    }
  }

  .header-button {
    height: 3rem;
    font-size: 1.25rem;
    line-height: 3rem;
    border-radius: 30px;
    padding: 10px 20px;
    margin-top: 30px;
  }
}
.section-img {
  width: 320px;
  margin-bottom: 8px;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}
.section-banner {
  margin: 0 auto;
  padding: 66px 20px 88px 20px;

  .section-wrapper {
    width: 1100px;
    padding: 0 20px;
    margin: 0 auto;
    .section-title {
      font-size: 36px;
      line-height: 50px;
      font-weight: 500;
    }
    .section-desc {
      font-size: 18px;
      line-height: 30px;
      margin-top: 26px;
      .number-item {
        padding-left: 16px;
        position: relative;
        margin-right: 36px;
        &::before {
          background-color: #ddd;
          border-radius: 2px;
          content: "";
          display: inline-block;
          height: 86%;
          left: 0;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
        }
        p {
          color: #000;
          font-weight: 500;
        }
        span {
          color: #db2221;
          font-size: 36px;
          font-weight: 500;
          letter-spacing: -0.73px;
          line-height: 44px;
        }
      }
    }
    .section-content {
      width: 660px;
      padding: 0 80px;
    }
  }
}
.section-bg {
  display: flex;
  height: 857px;
  min-width: 1280px;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 1920px 857px;
}
</style>
