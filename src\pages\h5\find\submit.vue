<template>
  <div class="mobile-container bg-[#F2F2F2]">
    <!-- 头部信息 -->
    <div class="w-full bg-white fixed top-0 z-10">
      <mobile-step-card :currentStep="1"></mobile-step-card>
      <div
        class="relative w-full h-[1.08rem] bg-white py-[0.18rem] flex justify-center px-[0.16rem] py-[0.36rem]"
      >
        <img
          loading="lazy"
          alt="back"
          class="w-[0.2rem] mr-[0.2rem] absolute left-[0.16rem] top-[50%] translate-y-[-50%]"
          @click="onBackClick"
          src="@/assets/icons/arrowLeft.svg"
          referrerpolicy="no-referrer"
        />
        <div class="text-[0.36rem] font-medium">
          {{ authStore.i18n("cm_submit.sendInquire") }}
        </div>
      </div>
    </div>

    <div
      class="text-[0.28rem] leading-[0.4rem] py-[0.32rem] px-[0.4rem] rounded-[0.12rem] m-[0.16rem] bg-white"
    >
      {{ authStore.i18n("cm_submit.messageTitle") }}
    </div>

    <n-form
      :rules="rules"
      ref="submitFromRef"
      class="mx-[0.16rem] my-[0.16rem]"
      :model="pageData.submitForm"
    >
      <div class="rounded-[0.12rem] bg-white px-[0.2rem] pt-[0.36rem]">
        <n-form-item
          path="submitName"
          class="input-form-item border-input-item"
          :label="authStore.i18n('cm_submit.username')"
        >
          <n-input
            v-trim
            :bordered="false"
            @keydown.enter.prevent
            v-model:value="pageData.submitForm.submitName"
            :placeholder="authStore.i18n('cm_submit.usernamePlaceholder')"
          />
        </n-form-item>
        <n-form-item
          path="countryId"
          class="input-form-item border-input-item"
          :label="authStore.i18n('cm_submit.country')"
        >
          <n-select
            :bordered="false"
            filterable
            value-field="id"
            label-field="countryEsName"
            :options="pageData.countryList"
            v-model:value="pageData.submitForm.countryId"
            @update:value="(value, option) => onSelectCountry(value, option)"
            :placeholder="authStore.i18n('cm_submit.countryPlaceholder')"
          />
        </n-form-item>
        <n-form-item
          path="whatsapp"
          class="input-form-item border-input-item"
          label="WhatsApp"
        >
          <n-input-group class="relative">
            <n-input
              v-trim
              readonly
              :bordered="false"
              class="!w-[1.12rem]"
              @keydown.enter.prevent
              v-model:value="pageData.submitForm.areaCode"
              placeholder="+000" />
            <div
              class="w-[0.02rem] h-[0.28rem] bg-[#e6e6e6] absolute left-[0.8rem] top-[28%] translate-y-[-50%]"
            ></div>
            <n-input
              v-trim
              clearable
              :bordered="false"
              @keydown.enter.prevent
              v-model:value="pageData.submitForm.whatsapp"
              :placeholder="authStore.i18n('cm_search.pleaseInputWhatsapp')"
          /></n-input-group>
        </n-form-item>
        <n-form-item
          path="email"
          class="input-form-item"
          :label="authStore.i18n('cm_search.email')"
        >
          <n-input
            v-trim
            clearable
            :bordered="false"
            @keydown.enter.prevent
            v-model:value="pageData.submitForm.email"
            :placeholder="authStore.i18n('cm_search.pleaseInputEmail')"
          />
        </n-form-item>
      </div>

      <div
        class="rounded-[0.12rem] my-[0.16rem] bg-white px-[0.2rem] pt-[0.36rem]"
      >
        <n-form-item
          path="postcode"
          class="input-form-item border-input-item"
          :label="authStore.i18n('cm_search.postcode')"
        >
          <n-input
            v-trim
            clearable
            :bordered="false"
            :maxlength="20"
            @keydown.enter.prevent
            v-model:value="pageData.submitForm.postcode"
            :placeholder="authStore.i18n('cm_search.pleaseInputTip')"
          />
        </n-form-item>
        <n-form-item
          path="address"
          class="input-form-item address-input-item"
          :label="authStore.i18n('cm_search.address')"
        >
          <n-input
            v-trim
            clearable
            :bordered="false"
            @keydown.enter.prevent
            v-model:value="pageData.submitForm.address"
            :placeholder="authStore.i18n('cm_search.pleaseInputTip')"
          />
        </n-form-item>
      </div>

      <div
        class="rounded-[0.12rem] my-[0.16rem] bg-white px-[0.2rem] pt-[0.36rem]"
      >
        <n-form-item
          path="remark"
          class="remark-input-item"
          :label="authStore.i18n('cm_submit.requirementDescription')"
        >
          <n-input
            v-trim
            clearable
            class="rounded-[0.12rem]"
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
            @keydown.enter.prevent
            v-model:value="pageData.submitForm.remark"
            :placeholder="authStore.i18n('cm_submit.requirementPlaceholder')"
          />
        </n-form-item>
      </div>
    </n-form>
    <div
      class="mx-[0.16rem] rounded-[0.12rem] bg-white my-[0.16rem] px-[0.2rem] py-[0.4rem]"
    >
      <div class="text-[0.32rem] leading-[0.36rem] font-medium text-[#1A1A1A]">
        {{ authStore.i18n("cm_find_inquireSummary") }}
      </div>
      <div class="flex flex-col gap-[0.24rem] mt-[0.32rem]">
        <div class="flex justify-between text-[0.28rem] leading-[0.28rem]">
          <span>{{ authStore.i18n("cm_find_quantityOfModels") }}</span>
          <span>
            <span class="font-medium">{{ pageData.stat?.goodsCount }}</span>
            {{
              pageData.stat?.goodsCount > 1
                ? authStore.i18n("cm_find_productModels")
                : authStore.i18n("cm_find_productModel")
            }}
          </span>
        </div>
        <div class="flex justify-between text-[0.28rem] leading-[0.28rem]">
          <span>{{ authStore.i18n("cm_find_quantityOfUnits") }}</span>
          <span>
            <span class="font-medium">{{
              pageData.stat?.selectSkuTotalQuantity
            }}</span>
            {{
              pageData.stat?.selectSkuTotalQuantity > 1
                ? authStore.i18n("cm_find_totalSkuUnits")
                : authStore.i18n("cm_find_totalSkuUnit")
            }}
          </span>
        </div>
        <div class="flex justify-between text-[0.28rem] leading-[0.28rem]">
          <span>{{ authStore.i18n("cm_find_itemsCost") }}:</span
          ><span
            class="text-[#e50113] font-medium"
            v-if="
              pageData.stat?.selectTotalSalePrice ||
              pageData.stat?.selectTotalSalePrice === 0
            "
            >{{ setUnit(pageData.stat?.selectTotalSalePrice) }}</span
          >
        </div>
        <div class="flex justify-between text-[0.28rem] leading-[0.28rem]">
          <n-popover trigger="hover" raw>
            <template #trigger>
              <div class="flex items-center cursor-pointer">
                <img
                  class="w-[0.28rem] h-[0.28rem] mr-[0.04rem]"
                  src="@/assets/icons/common/alert-circle.svg"
                  :alt="authStore.i18n('cm_goods.estimatedShippingCost')"
                  referrerpolicy="no-referrer"
                />
                {{ authStore.i18n("cm_goods.estimatedShippingCost") }}:
              </div>
            </template>
            <div
              style="
                z-index: 1;
                width: 300px;
                padding: 6px 14px;
                background-color: #fff4d4;
                transform-origin: inherit;
                border: 1px solid #f7ba2a;
              "
            >
              {{ authStore.i18n("cm_goods.freightAdjustmentPending") }}
            </div>
          </n-popover>

          <span v-if="pageData?.totalEstimateFreight">{{
            setUnit(pageData.totalEstimateFreight)
          }}</span>
          <span v-else class="text-[#4D4D4D]">{{
            authStore.i18n("cm_goods.pendingConfirmation")
          }}</span>
        </div>
      </div>
    </div>
    <div class="mx-[0.16rem] rounded-[0.12rem] bg-white mb-[0.16rem]">
      <n-collapse :show-arrow="false">
        <n-collapse-item
          v-for="goods in pageData.goodsList"
          :key="goods.goodsId"
          class="p-[0.16rem]"
        >
          <template #header>
            <mobile-goods-card :goods="goods" from="submit"></mobile-goods-card>
          </template>
          <template #header-extra="{ collapsed }">
            <icon-card
              v-if="collapsed"
              name="uil:angle-down"
              color="#797979"
              size="32"
            ></icon-card>
            <icon-card
              v-else
              name="uil:angle-up"
              color="#797979"
              size="32"
            ></icon-card>
          </template>
          <template #arrow><span class="hidden"></span></template>
          <!-- 三级分类 -->
          <div v-for="sku in goods.skuList" :key="sku.skuId">
            <mobile-sku-card
              :sku="sku"
              :goods="goods"
              :disabledInput="true"
              class="mb-[0.32rem]"
            ></mobile-sku-card>
          </div>
        </n-collapse-item>
      </n-collapse>
    </div>

    <!-- 底部信息 -->
    <div
      class="bg-white w-full px-[0.16rem] pt-[0.24rem] pb-[0.16rem] fixed bottom-0 z-10"
    >
      <div class="text-[0.28rem] leading-[0.28rem] text-[#7F7F7F] text-center">
        {{ authStore.i18n("cm_find_confirmWithoutPay") }}
      </div>
      <n-button
        size="large"
        color="#E50113"
        text-color="#fff"
        class="rounded-[0.12rem] px-[0.16rem] w-full text-[0.32rem] mt-[0.24rem] h-[0.8rem] font-medium"
        @click="onSubmit($event)"
        data-spm-box="checkout-to-order"
      >
        <img
          alt="list"
          loading="lazy"
          src="@/assets/icons/common/cart-submit-white.svg"
          class="w-[0.48rem] h-[0.48rem] mr-[0.16rem]"
          referrerpolicy="no-referrer"
        />
        {{ authStore.i18n("cm_submit.sendInquire") }}
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const router = useRouter();
import { useAuthStore } from "@/stores/authStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";

import MobileSkuCard from "./components/MobileSkuCard.vue";
import MobileGoodsCard from "./components/MobileGoodsCard.vue";
import MobileStepCard from "./components/MobileStepCard.vue";

const authStore = useAuthStore();
const config = useRuntimeConfig();

// 设置SEO规范链接
setSeoCanonical();

const userInfo = ref<object>({});
userInfo.value = config.public.userInfo as object;
const pageData = reactive<any>({
  stat: <any>{},
  countryRegexes: <any>{},
  goodsList: <any>[],
  countryList: <any>[],
  submitForm: <any>{
    submitName: "",
    whatsapp: "",
    email: "",
    remark: "",
    isTemporary: true,
    areaCode: "",
    countryId: null,
    postcode: "",
    address: "",
  },
  isHaveSubmitted: false,
  isSubmitLoading: false,
  defaultCountry: <any>{},
});
const submitFromRef = ref<FormInst | null>(null);

if (isEmptyObject(userInfo.value)) {
  navigateTo(`/h5/user/login?pageSource=/h5/find/submit`);
}

await onGetInquiry();
await onGetCountry();

const resetFontSize = computed(() => {
  if (process.client) {
    const fontSize = getComputedStyle(document.documentElement).fontSize;
    return parseFloat(fontSize);
  }
  return 50;
});

const rules: FormRules = {
  submitName: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_submit.usernamePlaceholder"),
    validator(rule: FormItemRule, value: any) {
      let remark;
      if (!value) {
        remark = `姓名：${value};报错提示：${authStore.i18n(
          "cm_submit.usernamePlaceholder"
        )}`;
        window?.MyStat?.addPageEvent("submit_name", remark);
        return false;
      } else {
        remark = `姓名：${value};`;
        window?.MyStat?.addPageEvent("submit_name", remark);
        return true;
      }
    },
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_submit.countryPlaceholder"),
    validator(rule: FormItemRule, value: any) {
      let remark;
      if (!value) {
        remark = `国家：${
          pageData.countryRegexes.countryCnName
        };报错提示：${authStore.i18n("cm_submit.countryPlaceholder")}`;
        window?.MyStat?.addPageEvent("submit_country", remark);
        return false;
      } else {
        remark = `国家：${pageData.countryRegexes?.countryCnName};（默认：${
          pageData.defaultCountry?.countryCnName || "无"
        }）`;
        window?.MyStat?.addPageEvent("submit_country", remark);
        return true;
      }
    },
  },

  whatsapp: {
    required: true,
    trigger: "blur",
    message: (() => {
      const phoneCountMessage = pageData.countryRegexes?.phoneCount
        ? `${authStore.i18n("cm_submit.whatsappTips")} ${
            pageData.countryRegexes?.phoneCount
          } ${authStore.i18n("cm_submit.whatsapp")}`
        : authStore.i18n("cm_submit.whatsappRequired");
      return `${phoneCountMessage}`;
    })(),
    validator(rule: FormItemRule, value: any) {
      let remark;
      const lengths =
        pageData.countryRegexes?.phoneCount &&
        pageData.countryRegexes.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            remark = `电话：${value};`;
            window?.MyStat?.addPageEvent("submit_phone", remark);
            return true;
          }
        }
      } else {
        if (value) {
          remark = `电话：${value};`;
          window?.MyStat?.addPageEvent("submit_phone", remark);
          return true;
        }
      }
      remark = `电话：${value};报错提示：${authStore.i18n(
        "cm_submit.whatsappTips"
      )} ${pageData.countryRegexes?.phoneCount} ${authStore.i18n(
        "cm_submit.whatsapp"
      )}`;
      window?.MyStat?.addPageEvent("submit_phone", remark);
      return false;
    },
  },
  email: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_search.emailTips"),
    validator(rule: FormItemRule, value: any) {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      let remark = `邮箱：${value}`;
      if (!pattern.test(value)) {
        remark = `邮箱：${value};报错提示：${authStore.i18n(
          "cm_search.emailTips"
        )}`;
      }
      window?.MyStat?.addPageEvent("submit_email", remark);
      return pattern.test(value);
    },
  },
};

async function onGetInquiry() {
  const inquiryInfo = authStore.getInquiryInfo();
  pageData.goodsList = inquiryInfo?.goodsList;
  pageData.totalEstimateFreight = inquiryInfo?.totalEstimateFreight;
  pageData.stat = inquiryInfo?.stat;
  if (inquiryInfo?.lastInquiry && !isEmptyObject(inquiryInfo?.lastInquiry)) {
    const {
      submitName,
      countryId,
      areaCode,
      whatsapp,
      email,
      postcode,
      address,
    } = inquiryInfo.lastInquiry;
    pageData.submitForm.submitName = submitName;
    pageData.submitForm.countryId = countryId;
    pageData.submitForm.areaCode = areaCode;
    pageData.submitForm.whatsapp = whatsapp;
    pageData.submitForm.email = email;
    pageData.submitForm.postcode = postcode;
    pageData.submitForm.address = address;
  } else {
    pageData.submitForm.email = userInfo?.value?.username;
  }
  console.log("pageData.submitForm.address :>> ", pageData.submitForm.address);
}

async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
    // 优先使用用户上次询盘的国家
    if (pageData.submitForm.countryId) {
      res?.data.map((country: any) => {
        if (country.id === pageData.submitForm.countryId) {
          pageData.submitForm.areaCode = country?.areaCode;
          pageData.countryRegexes = country;
          pageData.defaultCountry = country;
        }
      });
    } else {
      // 用户没有上次询盘信息，则使用基于IP地址定位所属国家
      if (config.public.defaultCountryCode) {
        res?.data.map((country: any) => {
          if (country.countryCodeTwo === config.public.defaultCountryCode) {
            pageData.submitForm.countryId = country.id;
            pageData.submitForm.areaCode = country?.areaCode;
            pageData.countryRegexes = country;
            pageData.defaultCountry = country;
          }
        });
      }
    }
  }
}

function onSelectCountry(value: any, country: any) {
  pageData.submitForm.areaCode = country?.areaCode;
  pageData.countryRegexes = country;
  // 如果有长度校验 则校验长度
  if (pageData.countryRegexes?.phoneCount) {
    rules["whatsapp"].message = `${authStore.i18n("cm_submit.whatsappTips")} ${
      pageData.countryRegexes.phoneCount
    } ${authStore.i18n("cm_submit.whatsapp")}`;
  } else {
    // 没有长度校验 校验必填
    rules["whatsapp"].message = `${authStore.i18n(
      "cm_submit.whatsappRequired"
    )}`;
  }
}

async function onSubmit(event: any) {
  await submitFromRef.value?.validate();
  if (pageData.isSubmitLoading) return;
  const params = <any>[];
  pageData.goodsList.forEach((goods: any) => {
    goods.skuList.forEach((sku: any) => {
      params.push({
        quantity: sku.buyQty,
        skuId: sku.skuId,
        spm: sku.spm,
        routeId: goods.routeId,
        padc: sku.padc,
      });
    });
  });
  const {
    submitName,
    countryId,
    whatsapp,
    email,
    remark,
    areaCode,
    postcode,
    address,
  } = pageData.submitForm;
  let paramsObj = {
    submitName: submitName?.trim(),
    countryId: countryId?.trim(),
    whatsapp: whatsapp?.trim(),
    email: email?.trim(),
    remark: remark?.trim(),
    isTemporary: false,
    areaCode,
    params,
    fromCart: route.query.fromCart == "false" ? false : true, //不从购物车过来 需要传给服务端false
    postcode,
    address,
    siteId: window.siteData.siteInfo.id,
  };

  pageData.isSubmitLoading = true;
  try {
    const res: any = await useSubmitInquiry(paramsObj);
    if (res?.result?.code === 200) {
      pageData.isHaveSubmitted = true;
      authStore.getCartList();
      if (!!window?.fbq) {
        window?.fbq("track", "Purchase", {
          content_type: "product",
          currency: "USD",
          value: pageData?.stat?.totalSalePrice,
          contents: pageData.goodsList,
        });
      }

      if (!!window?.ttq) {
        window?.ttq?.track("SubmitForm", {
          content_type: "product",
          currency: "USD",
          value: pageData?.stat?.totalSalePrice,
          contents: pageData.goodsList,
        });
      }
      const url = navigateUrl(
        `/h5/find/submit-thankyou`,
        {
          email: email?.trim(),
          whatsapp: areaCode + whatsapp,
          firstSubmit: res?.data?.isFirstSubmit || false,
        },
        event
      );
      setTimeout(() => {
        window.location.replace(url);
      }, 200);
    } else {
      window?.MyStat?.addPageEvent(
        "submit_looking_error",
        `询盘提交错误:${res?.result?.message}`
      );
      showToast(res?.result?.message);
      pageData.isSubmitLoading = false;
    }
  } catch (error) {
    pageData.isSubmitLoading = false;
  }
}

onBeforeUnmount(() => {
  !pageData.isHaveSubmitted && useSubmitTemporary(pageData.submitForm);
});

function onBackClick() {
  window.location.replace("/h5/find");
}
</script>

<style scoped lang="scss">
:deep(.n-card__content) {
  padding: 0 !important;
}
.mobile-container {
  height: 100%;
  overflow: auto;
  padding-top: 3.4rem;
  padding-bottom: 1.72rem;
}
:deep(.n-collapse-item__header) {
  padding: 0 !important;
}
:deep(.n-collapse-item) {
  margin-top: 0 !important;
}
:deep(.n-form-item) {
  --n-label-height: initial;
  line-height: unset;
  .n-form-item-label {
    line-height: 0.28rem;
    margin-bottom: 0.24rem;
  }
}

:deep(.n-input-wrapper) {
  padding-left: 0.16rem;
  padding-right: 0.16rem;
}
:deep(.input-form-item) {
  position: relative;
  .n-form-item-label {
    padding: 0;
  }
  .n-form-item-feedback-wrapper {
    color: #e50113;
    min-height: 0.36rem;
    padding-left: 0;
    padding-top: 0.04rem;
  }
  .n-input-wrapper {
    padding-left: 0;
    padding-right: 0;
  }
  .n-select {
    padding-left: 0;
    padding-right: 0;
    .n-base-selection-input {
      padding-left: 0 !important;
    }
    .n-base-selection-overlay {
      align-items: flex-start;
      padding-left: 0 !important;
    }
  }
}
:deep(.border-input-item) {
  .n-input-wrapper {
    border-bottom: 0.02rem solid #e6e6e6;
  }
  .n-select {
    border-bottom: 0.02rem solid #e6e6e6;
  }

  .n-input .n-input__input-el {
    padding-bottom: 0.36rem;
  }
  .n-base-selection-input {
    padding-bottom: 0.36rem;
  }
}

:deep(.n-form-item-label) {
  min-height: auto;
  --n-label-height: unset;
  .n-form-item-label__text {
    font-size: 0.28rem;
    line-height: 0.28rem;
    font-weight: 500;
  }
}
:deep(.n-form-item .n-input .n-input__input-el) {
  font-size: 0.28rem;
  line-height: 0.28rem;
  height: initial;
}

:deep(.n-base-selection-label) {
  height: initial;
  font-size: 0.28rem;
  line-height: 0.28rem;
  min-height: auto;
  --n-height: unset;
  --n-label-height: unset;
  .n-base-selection-overlay__wrapper {
    font-size: 0.28rem;
    line-height: 0.28rem;
  }
  .n-base-suffix {
    top: 0.16rem;
  }
}

:deep(.n-base-selection-input) {
  font-size: 0.28rem;
  line-height: 0.28rem;
  height: initial;
  padding-bottom: 0.36rem;
  padding-top: 0;
}

:deep(.n-form-item .n-form-item-blank) {
  min-height: auto;
}

:deep(.n-form-item .n-input__placeholder) {
  font-size: 0.28rem;
  align-items: flex-start !important;
}

:deep(.n-input--textarea) {
  height: fit-content;
  --n-height: unset;
  line-height: unset;
  --n-line-height-textarea: unset;
  .n-input-wrapper {
    --n-height: unset;
    padding-bottom: 0.2rem;
  }
  .n-input__textarea-el {
    height: fit-content;
    line-height: unset;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .n-input__placeholder {
    height: fit-content;
    line-height: 0.32rem !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}

:deep(.remark-input-item) {
  .n-input__textarea-el {
    padding-top: 0.16rem !important;
    padding-bottom: 0.16rem !important;
  }
  .n-form-item-label {
    height: 0.28rem;
    padding-bottom: 0;
    margin-bottom: 0.24rem;
  }
  .n-form-item-feedback-wrapper {
    min-height: 0.36rem;
  }
  .n-input__placeholder {
    height: fit-content;
    padding-top: 0.16rem !important;
    padding-bottom: 0.16rem !important;
  }
}
</style>
