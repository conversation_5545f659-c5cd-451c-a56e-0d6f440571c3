<template>
  <div class="w-full min-h-screen bg-white">
    <mobile-search-bar></mobile-search-bar>
    <div class="px-[20px]">
      <div class="breadcrumb text-[0.28rem] leading-[0.28rem] mt-[30px]">
        <div class="breadcrumb-item">
          <a href="/">
            <img
              loading="lazy"
              src="@/assets/icons/article/home-black.svg"
              alt="home"
              class="home-icon h-[0.28rem]"
            />
          </a>
        </div>
        <img
          loading="lazy"
          src="@/assets/icons/article/arrow-right.svg"
          alt="arrow"
          class="arrow-icon"
        />
        <div class="breadcrumb-item">
          <a href="/h5/blog">
            <div class="breadcrumb-link min-w-[0.6rem] text-center">Blog</div>
          </a>
        </div>
        <img
          loading="lazy"
          src="@/assets/icons/article/arrow-right.svg"
          alt="arrow"
          class="arrow-icon"
        />
        <!-- 当前标题 -->
        <div class="breadcrumb-item">
          <span class="breadcrumb-link active max-w-[5rem] line-clamp-1">
            {{ pageData.articleDetail.title }}
          </span>
        </div>
      </div>

      <div
        class="text-[28px] leading-[0.64rem] text-[#333] font-medium mt-[0.64rem]"
      >
        {{ pageData.articleDetail.title }}
      </div>

      <div
        v-if="pageData.articleDetail?.udate"
        class="text-[#7F7F7F] text-[0.3rem] leading-[0.3rem] italic mt-[0.52rem]"
      >
        <span>Publicado en </span>
        <span class="text-[#4290F7]">{{
          formatDateOnly(pageData.articleDetail.udate)
        }}</span>
      </div>

      <div
        data-spm-box="article-inner-link"
        v-if="pageData.articleDetail?.content"
        v-html="pageData.articleDetail?.content"
        class="whitespace-pre-wrap mt-[0.64rem]"
      ></div>
    </div>

    <mobile-page-footer></mobile-page-footer>
  </div>
</template>

<script setup lang="ts">
import { formatDateOnly } from "@/utils/date";

const route = useRoute();
const pageData = reactive<any>({
  articleDetail: "",
});

await onGetArticle();

useHead({
  title: `${pageData.articleDetail?.title} -Chilatshop blog`,
  meta: [
    {
      name: "description",
      content: sanitizeMetaDescription(pageData.articleDetail.brief),
    },
  ],
});

async function onGetArticle() {
  const res: any = await useArticleDetail({
    id: route.query.id,
    title: route.query.title,
    articleCode: route.params.id, // 使用路由参数中的id
  });
  if (res?.result?.code === 200) {
    pageData.articleDetail = res?.data;
  }
}
</script>

<style scoped lang="scss">
.blog-wrapper {
  height: auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.home-icon:hover {
  content: url("@/assets/icons/article/home-red.svg");
}

.breadcrumb-link {
  color: #7f7f7f;
  transition: all 0.3s;
  cursor: pointer;
}

.breadcrumb-link:hover,
.breadcrumb-link.active {
  color: #333;
  font-weight: 500;
}

.arrow-icon {
  width: 0.12rem;
  margin: 0 0.16rem;
  filter: brightness(0) saturate(100%) invert(43%) sepia(0%) saturate(0%)
    hue-rotate(193deg) brightness(96%) contrast(90%);
}
</style>
