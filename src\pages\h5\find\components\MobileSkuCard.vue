<template>
  <div
    class="w-full bg-[#F2F2F2] text-[#333] pt-[0.24rem] rounded-[0.12rem] text-[0.28rem]"
  >
    <div class="flex items-center justify-between px-[0.16rem]">
      <div class="flex items-center">
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          class="w-[0.52rem] h-[0.52rem] shrink-0 mr-[0.24rem]"
          :src="sku.skuImage"
          :alt="getSkuName(sku)"
          :img-props="{ referrerpolicy: 'no-referrer' }"
        />
        <n-ellipsis :line-clamp="1" :tooltip="false">
          {{ getSkuName(sku) }}
        </n-ellipsis>
        <slot name="spec"></slot>
      </div>
      <slot name="delete"></slot>
    </div>
    <div
      class="flex items-center justify-between p-[0.16rem] border-b-1 border-solid border-[#D7D7D7]"
    >
      <div>
        <span class="mr-[0.08rem]">
          <span class="text-[#222222] font-medium text-[0.26rem]">{{
            setUnit(sku.salePrice)
          }}</span>
          / {{ goods.goodsPriceUnitName }}</span
        >
        <n-popover trigger="hover" placement="bottom" v-if="!disabledInput">
          <template #trigger>
            <icon-card
              size="0.36rem"
              name="mingcute:warning-line"
              color="#797979"
              class="mr-[0.16rem] cursor-pointer"
            ></icon-card>
          </template>
          <div class="text-[0.28rem]">
            <div>
              <icon-card
                size="0.36rem"
                name="mingcute:warning-line"
                color="#797979"
                class="cursor-pointer mr-[0.08rem]"
              ></icon-card>
              <span class="font-medium">
                {{ authStore.i18n("cm_find.stepPrices") }}
              </span>
            </div>
            <div
              v-for="(step, index) in sku?.stepPrices"
              :key="index"
              class="mt-[0.24rem] text-[#797979] text-[0.24rem] leading-[0.32rem] flex items-center justify-between px-[0.16rem]"
              :class="{
                '!text-[#DB1925]': pageData.currentStepPriceEnd == step.end,
              }"
            >
              <span>
                {{ filterPriceRange(step) }}
                {{ goods?.goodsPriceUnitName }}
              </span>
              <span>{{ setUnit(step.price) }}</span>
            </div>
          </div>
        </n-popover>
      </div>
      <!-- readonly 这里设置只读 是因为某些浏览器设置禁用状态 input值没有显示 -->
      <n-input-number
        :min="0"
        :max="10000000"
        :precision="0"
        :step="step"
        v-model:value="sku.buyQty"
        class="w-[2.88rem] input-number"
        button-placement="both"
        :on-update:value="(value) => onCartQtyUpdate(value, sku)"
        :readonly="disabledInput"
      />
    </div>
    <div
      class="flex text-black font-medium p-[0.16rem] justify-end text-[0.28rem]"
    >
      {{ setUnit(sku.subtotalSalePrice) }}
    </div>
  </div>
</template>

<script setup lang="ts" name="SkuCard">
import { setUnit } from "@/utils/mixin";
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();

const emit = defineEmits(["onCartQtyUpdate"]);
const props = defineProps({
  goods: {
    type: Object,
    default: () => {},
  },
  sku: {
    type: Object,
    default: () => {},
  },
  disabledInput: {
    type: Boolean,
    default: false,
  },
  step: {
    type: Number,
    default: 1,
  },
});
const pageData = reactive(<any>{
  currentStepPriceEnd: null,
});
watch(
  () => props.goods,
  (newVal: any) => {
    if (newVal) {
      for (const step of props.sku.stepPrices) {
        if (newVal.skuTotalQuantity <= step.end || step.end === -1) {
          pageData.currentStepPriceEnd = step.end;
          break;
        }
      }
    }
  },
  { immediate: true, deep: true }
);

onMounted(async () => {});

function getSkuName(sku: any) {
  const names = sku?.specItemList?.map((spec: any) => {
    return `${spec.groupName}: ${spec.itemName}`;
  });
  return names?.join("; ");
}

// 销售规格的加购数修改
function onCartQtyUpdate(value: any, sku: any) {
  emit("onCartQtyUpdate", value, sku, props.goods);
}

function filterPriceRange(price: any) {
  if (price.end == -1) {
    return `>=${price.start}`;
  } else {
    if (price.start == price.end) {
      return `${price.start}`;
    }
    return `${price.start}-${price.end}`;
  }
}
</script>

<style scoped lang="scss">
:deep(.input-number .n-input__input-el) {
  text-align: center;
}
</style>
