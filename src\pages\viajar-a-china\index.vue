<template>
  <div class="page-container">
    <div class="page-header">
      <div class="w-[1280px] px-[64px] py-[36px] mx-auto">
        <div class="relative z-2">
          <div class="w-[150px] h-[36px]">
            <img
              loading="lazy"
              :src="configStore.pageTheme.chilatWhiteLogo"
              alt="logo"
              class="w-[150px]"
            />
          </div>
          <div
            class="w-[550px] text-[52px] leading-[62px] font-medium mt-[82px]"
            v-html="authStore.i18n('cm_visit.excellenceCenter')"
          ></div>

          <div
            class="w-[480px] text-[26px] leading-[52px] mt-[52px]"
            v-html="authStore.i18n('cm_visit.transformJourney')"
          ></div>
        </div>
      </div>
    </div>
    <div class="w-[1280px] mx-auto text-[#333] px-[64px] pb-[190px]">
      <div class="w-full mt-[120px] text-center">
        <div class="text-[34px] leading-[34px] font-medium">
          {{ authStore.i18n("cm_visit.travelToChina") }}
        </div>
        <div class="w-full text-[18px] leading-[22px] text-[#7F7F7F] mt-[16px]">
          <div>
            {{ authStore.i18n("cm_visit.faceToFaceMeetings") }}
          </div>
          <div>
            {{ authStore.i18n("cm_visit.tripToChina") }}
          </div>
        </div>
        <div class="w-[50px] h-[3px] bg-[#E50113] mt-[36px] mx-auto"></div>
        <div class="w-full mt-[78px] flex justify-between">
          <div
            v-for="(item, index) in visitChinaData"
            :key="index"
            class="w-[230px] flex flex-col items-center"
          >
            <img
              loading="lazy"
              :src="item.imgUrl"
              :alt="item.title"
              class="w-[48px]"
            />
            <div
              class="min-h-[40px] text-[20px] leading-[20px] mt-[18px] flex items-center"
            >
              {{ item.title }}
            </div>
            <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[14px]">
              {{ item.desc }}
            </div>
          </div>
        </div>
      </div>
      <div class="w-full mt-[170px]">
        <div class="text-[34px] leading-[34px] font-medium text-center">
          {{ authStore.i18n("cm_visit.takeCareOf") }}
        </div>
        <div
          class="w-[954px] mx-auto text-[18px] leading-[22px] text-[#7F7F7F] text-center mt-[16px]"
        >
          {{ authStore.i18n("cm_visit.comprehensiveService") }}
        </div>
        <div class="w-[50px] h-[3px] bg-[#E50113] mt-[36px] mx-auto"></div>
        <div class="flex justify-between flex-wrap mt-[78px]">
          <div
            v-for="(item, index) in visitProcData"
            :key="index"
            class="relative w-[372px] h-[446px] bg-white border border-[#333] rounded-[20px] pt-[248px] px-[27px] mb-[28px]"
          >
            <img
              loading="lazy"
              :src="item.imgUrl"
              alt="visitChina"
              class="absolute top-0 left-0"
            />
            <div class="text-[20px] leading-[20px]">
              {{ item.title }}
            </div>
            <n-space
              vertical
              :style="{ gap: '4px 0' }"
              class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[12px]"
            >
              <div v-for="desc in item.descData" :key="desc">
                {{ desc }}
              </div>
            </n-space>
          </div>
        </div>
      </div>
      <div class="w-full mt-[120px]">
        <div class="flex">
          <img loading="lazy" :src="rightArrow" alt="arrow" class="mr-[10px]" />
          <span class="text-[18px] leading-[18px] text-[#7F7F7F]">{{
            authStore.i18n("cm_visit.comeToChina")
          }}</span>
        </div>
        <div class="text-[34px] leading-[34px] font-medium mt-[24px]">
          {{ authStore.i18n("cm_visit.howWeWork") }}
        </div>
        <div class="mt-[50px] flex items-center">
          <div
            class="rounded-[12px] w-[635] h-[357px] overflow-hidden bg-white mr-[34px]"
          >
            <video-you-tube
              :width="635"
              :height="357"
              :key="pageData.activatedWorkVideo?.id"
              :poster="pageData.activatedWorkVideo.poster"
              :youtubeId="pageData.activatedWorkVideo?.id"
              :title="pageData.activatedWorkVideo?.title"
              :titleCh="pageData.activatedWorkVideo?.titleCh"
            ></video-you-tube>
          </div>
          <n-space vertical :style="{ gap: '40px 0' }" class="ml-auto">
            <div
              v-for="item in workVideoData"
              :key="item.id"
              class="flex items-center cursor-pointer"
              @click="onPlayVideo(item)"
            >
              <img
                loading="lazy"
                :src="
                  item.id === pageData.activatedWorkVideo?.id ? videoing : video
                "
                alt="video"
                class="mr-[10px]"
              />
              <div
                :class="
                  item.id === pageData.activatedWorkVideo?.id
                    ? '!text-[#e50113]'
                    : ''
                "
                class="w-[450px] text-[20px] leading-[20px] text-medium"
              >
                {{ item.title }}
              </div>
            </div>
          </n-space>
        </div>
      </div>
    </div>
    <div class="bg-[#D5141B] text-[#FFF] relative z-1">
      <div class="w-[1280px] mx-auto relative px-[38px] py-[80px]">
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          class="w-[688px] absolute right-[38px] top-[-50px]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/06/aaf4cf98-ef26-4a1f-a873-7823ee8bac88.png"
        />
        <div class="text-[40px] leading-[48px] font-medium">
          {{ authStore.i18n("cm_visit.whyUs") }}
        </div>
        <div class="w-[50px] h-[3px] bg-[#fff] mt-[104px]"></div>
        <div class="flex justify-between mt-[112px]">
          <div
            v-for="(item, index) in whyUsData"
            :key="index"
            class="w-[250px] h-[262px] border-b-1 border-[#FFF]"
          >
            <div class="flex text-[30px] leading-[30px] font-medium">
              <img
                loading="lazy"
                :src="rightArrowWhite"
                :alt="item.title"
                class="mr-[12px]"
              />
              0{{ index + 1 }}
            </div>
            <div
              class="min-h-[60px] text-[20px] leading-[20px] font-medium mt-[30px]"
              v-html="item.title"
            ></div>
            <div class="text-[16px] leading-[20px] text-[#F2F2F2] mt-[20px]">
              {{ item.desc }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-[#F7F7F7] overflow-hidden">
      <div class="w-[1280px] h-[754px] mx-auto relative flex flex-col">
        <div
          class="w-[1068px] h-[1068px] rounded-full bg-white absolute top-[-64px] right-[-68px]"
        ></div>
        <div class="relative z-1">
          <div
            class="text-[34px] leading-[34px] font-medium text-center mt-[200px]"
          >
            {{ authStore.i18n("cm_visit.knowPeople") }}
          </div>
          <div class="w-[50px] h-[3px] bg-[#E50113] mt-[36px] mx-auto"></div>
          <div class="flex justify-between px-[12px]">
            <div
              class="w-[304px] h-[171px] rounded-[20px] relative mt-[50px] overflow-hidden"
              v-for="(video, index) in userVideoData"
              :key="video.id"
              @click="onOpenVideo(video)"
            >
              <n-image
                lazy
                preview-disabled
                :src="video.poster"
                class="img"
                object-fit="cover"
              />
              <img
                loading="lazy"
                :src="videoPlay"
                alt="video"
                class="w-[70px] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="whatsapp-wrapper w-[1280px] mx-auto">
      <div
        class="w-[540px] text-[42px] leading-[84px] font-semibold text-[#333] pt-[73px] pl-[40px] relative"
      >
        <span v-html="authStore.i18n('cm_nota.clickWhatsApp')"></span>
        <img
          loading="lazy"
          src="@/assets/icons/open/greenLine.svg"
          alt="line"
          class="absolute top-[139px] right-[26px]"
        />
      </div>
      <img
        loading="lazy"
        alt="click"
        class="icon"
        @click="onHandleWhatsAppClick('bottom')"
        src="@/assets/icons/open/whatsappClick.png"
      />
      <n-button
        color="#E50113"
        @click="onHandleWhatsAppClick('bottom')"
        data-spm-box="potential_user_note_submit"
        class="button"
      >
        <div
          class="flex items-center justify-center text-[34px] leading-[34px] font-semibold"
          v-html="authStore.i18n('cm_nota.clickToWhatsapp')"
        ></div>
      </n-button>
    </div>
    <div class="side-affix">
      <icon-card
        name="logos:whatsapp-icon"
        size="22px"
        title="W/app"
        :border="true"
        :multiple="true"
        @click="onHandleWhatsAppClick()"
      ></icon-card>
    </div>
  </div>
  <video-modal ref="videoModalRef"></video-modal>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";

// 设置SEO规范链接
setSeoCanonical();

import rightArrow from "@/assets/icons/open/rightArrow.svg";
import rightArrowWhite from "@/assets/icons/open/rightArrowWhite.svg";
import video from "@/assets/icons/open/video.svg";
import videoing from "@/assets/icons/open/videoing.svg";
import videoPlay from "@/assets/icons/open/videoPlay.svg";
import money from "@/assets/icons/open/money.svg";
import shopping from "@/assets/icons/open/shopping.svg";
import find from "@/assets/icons/open/find.svg";
import cooperation from "@/assets/icons/open/cooperation.svg";
import quotation from "@/assets/icons/open/quotation.svg";
import travelTips from "@/assets/icons/open/travelTips.png";
import accommodation from "@/assets/icons/open/accommodation.png";
import expertAdvice from "@/assets/icons/open/expertAdvice.png";
import interpreters from "@/assets/icons/open/interpreters.png";
import orderQualityControl from "@/assets/icons/open/orderQualityControl.png";
import logisticsAssistance from "@/assets/icons/open/logisticsAssistance.png";
import workVideoPoster1 from "@/assets/icons/open/workVideoPoster1.png";
import workVideoPoster2 from "@/assets/icons/open/workVideoPoster2.png";
import workVideoPoster3 from "@/assets/icons/open/workVideoPoster3.png";
import workVideoPoster4 from "@/assets/icons/open/workVideoPoster4.png";
import workVideoPoster5 from "@/assets/icons/open/workVideoPoster5.png";

import userVideoPoster1 from "@/assets/icons/open/userVideoPoster1.png";
import userVideoPoster2 from "@/assets/icons/open/userVideoPoster2.png";
import userVideoPoster3 from "@/assets/icons/open/userVideoPoster3.png";
import userVideoPoster4 from "@/assets/icons/open/userVideoPoster4.png";

const authStore = useAuthStore();
const configStore = useConfigStore();
const videoModalRef = ref<any>(null);

const visitChinaData = [
  {
    imgUrl: money,
    title: authStore.i18n("cm_visit.competitivePrice"),
    desc: authStore.i18n("cm_visit.competitivePriceDesc"),
  },
  {
    imgUrl: shopping,
    title: authStore.i18n("cm_visit.wideRange"),
    desc: authStore.i18n("cm_visit.wideRangeDesc"),
  },
  {
    imgUrl: find,
    title: authStore.i18n("cm_visit.qualityAssurance"),
    desc: authStore.i18n("cm_visit.qualityAssuranceDesc"),
  },
  {
    imgUrl: cooperation,
    title: authStore.i18n("cm_visit.buildRelations"),
    desc: authStore.i18n("cm_visit.buildRelationsDesc"),
  },
];

const visitProcData = [
  {
    imgUrl: travelTips,
    title: authStore.i18n("cm_visit.travelTips"),
    descData: [
      authStore.i18n("cm_visit.researchMarkets"),
      authStore.i18n("cm_visit.prioritizePlaces"),
    ],
  },
  {
    imgUrl: accommodation,
    title: authStore.i18n("cm_visit.accommodation"),
    descData: [authStore.i18n("cm_visit.hotelArrange")],
  },
  {
    imgUrl: expertAdvice,
    title: authStore.i18n("cm_visit.expertAdvice"),
    descData: [authStore.i18n("cm_visit.professionalAdvisors")],
  },
  {
    imgUrl: interpreters,
    title: authStore.i18n("cm_visit.interpreters"),
    descData: [authStore.i18n("cm_visit.languageBarriers")],
  },
  {
    imgUrl: orderQualityControl,
    title: authStore.i18n("cm_visit.orderQualityControl"),
    descData: [authStore.i18n("cm_visit.trackProdProc")],
  },
  {
    imgUrl: logisticsAssistance,
    title: authStore.i18n("cm_visit.logisticsAssistance"),
    descData: [
      authStore.i18n("cm_visit.shipmentAdvice"),
      authStore.i18n("cm_visit.logisticalSupport"),
    ],
  },
];

const workVideoData = [
  {
    id: "b7X9D3BLvMc",
    poster: workVideoPoster1,
    title: authStore.i18n("cm_visit.hotelReservation"),
    titleCh: "1.发出邀请信/酒店预订",
  },
  {
    id: "VCgjAWzHB1A",
    poster: workVideoPoster2,
    title: authStore.i18n("cm_visit.marketVisit"),
    titleCh: "2.在机场接待并参观市场",
  },
  {
    id: "wWtOhkPDg5A",
    poster: workVideoPoster3,
    title: authStore.i18n("cm_visit.productionTracking"),
    titleCh: "3.下订单并跟踪生产",
  },
  {
    id: "GN6F6oLssUw",
    poster: workVideoPoster4,
    title: authStore.i18n("cm_visit.qualityInspection"),
    titleCh: "4.接收产品并进行质量检查",
  },
  {
    id: "KbpxdDskNsQ",
    poster: workVideoPoster5,
    title: authStore.i18n("cm_visit.loadContainer"),
    titleCh: "5.装载集装箱并组织运输",
  },
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    poster: userVideoPoster1,
    titleCh: "1.我们的客户对我们的看法7",
  },
  {
    id: "Tj0nrnhxgXw",
    poster: userVideoPoster2,
    titleCh: "2.我们的客户对我们的看法2",
  },
  {
    id: "_omi5a-pHkA",
    poster: userVideoPoster3,
    titleCh: "3.我们的客户对我们的看法6",
  },
  {
    id: "4FVIz0PvEcE",
    poster: userVideoPoster4,
    titleCh: "4.我们的客户对我们的看法5",
  },
];

const whyUsData = [
  {
    title: authStore.i18n("cm_visit.famousBrand"),
    desc: authStore.i18n("cm_visit.famousBrandDesc"),
  },
  {
    title: authStore.i18n("cm_visit.experiencedTeam"),
    desc: authStore.i18n("cm_visit.experiencedTeamDesc"),
  },
  {
    title: authStore.i18n("cm_visit.factoryResources"),
    desc: authStore.i18n("cm_visit.factoryResourcesDesc"),
  },
  {
    title: authStore.i18n("cm_visit.alliesTeam"),
    desc: authStore.i18n("cm_visit.alliesTeamDesc"),
  },
];

const pageData = reactive(<any>{
  activatedWorkVideo: workVideoData[0],
});

function scrollToNav(dom: any) {
  if (!dom) return;
  const element = document.getElementById(dom);
  if (!element) return;
  const elementRect = element.getBoundingClientRect();
  const offsetPosition = elementRect.top + window.scrollY;
  window.scrollTo({
    top: offsetPosition,
    behavior: "smooth",
  });
}

function onPlayVideo(val: any) {
  pageData.activatedWorkVideo = val;
}

function onOpenVideo(video: any) {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}

async function onHandleWhatsAppClick(val?: any) {
  let clickSource = "viajar-a-china页面悬浮按钮点击WhatsApp";
  if (val) {
    clickSource = "viajar-a-china页面底部按钮点击WhatsApp";
  }
  window?.MyStat?.addPageEvent(
    "potential_user_click_whatsapp",
    clickSource,
    true
  );
  let paramsObj = {
    clickSource,
  };
  const res: any = await useClickWhatsapp(paramsObj);
  if (res?.result?.code === 200) {
    onWhatsAppClick();
  } else {
    showToast(res?.result?.message);
  }
}
</script>

<style scoped lang="scss">
.page-container {
  min-width: 1280px;
  height: auto;
  min-height: 100vh;
  color: #333;
}

.page-header {
  position: relative;
  width: 100%;
  height: 584px;
  background-size: cover;
  color: #fff;
  background-image: url("@/assets/icons/open/svipVisitBg.png");
  background-repeat: no-repeat;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(21, 27, 35, 0.25);
    z-index: 1;
  }
}
.side-affix {
  position: fixed;
  right: 28px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
}

.whatsapp-wrapper {
  position: relative;
  height: 573px;
  background-size: 100%100%;
  color: #fff;
  background-image: url("@/assets/icons/open/whatsappBg.png");
  .icon {
    position: absolute;
    top: -73px;
    width: 268px;
    right: 16px;
    z-index: 1;
  }
  .button {
    position: absolute;
    top: 63px;
    right: 40px;
    height: fit-content;
    display: inline-flex;
    padding: 28px 64px;
    align-items: center;
    gap: 8px;
    border-radius: 1200px;
    border: 6px solid var(---100, #fff);
    background: #25d366;
    box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.5);
  }
}
</style>
