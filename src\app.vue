<template>
  <div>
    <NuxtPwaManifest />
    <n-config-provider
      inline-theme-disabled
      :theme-overrides="themeOverrides"
      :locale="locale"
      :date-locale="dateLocale"
    >
      <n-message-provider>
        <!-- 站点选择弹窗 -->
        <site-select-modal v-if="!hideSiteSelectModal" />
        <NuxtLayout name="mobile" v-if="isMobileView">
          <NuxtLoadingIndicator :color="pageTheme.loading.color" />
          <NuxtPage keepalive />
        </NuxtLayout>
        <NuxtLayout name="default" v-else>
          <NuxtLoadingIndicator :color="pageTheme.loading.color" />
          <NuxtPage keepalive />
        </NuxtLayout>
      </n-message-provider>
    </n-config-provider>
  </div>
</template>

<script lang="ts" setup>
import { NConfigProvider, type GlobalThemeOverrides } from "naive-ui";
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import SiteSelectModal from "@/components/SiteSelectModal.vue";
import { initSiteInfo } from "@/utils/siteUtils";

const { isMobile } = useDevice();
const route = useRoute();
const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const isMobileView = ref(false);
const isFullscreen = ref(false);
const initialDeviceType = ref(isMobile);
const pageData = reactive(<any>{});
const config = useRuntimeConfig();

// 首次加载时使用 useDevice 判断设备类型
isMobileView.value = isMobile;

const locale = computed(() => useAuthStore().customLocal);
const dateLocale = computed(() => useAuthStore().customLocalDate);
const pageTheme = computed(() => useConfigStore().getPageTheme);

// 检测是否处于全屏状态
function checkFullscreen() {
  return !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  );
}

// 监听全屏变化
function handleFullscreenChange() {
  const wasFullscreen = isFullscreen.value;
  isFullscreen.value = checkFullscreen();

  // 如果从全屏状态退出，检查是否需要重定向
  if (wasFullscreen && !isFullscreen.value) {
    checkAndRedirect();
  }
}

// 设备重定向检查
function checkAndRedirect() {
  // 设备检测：结合UserAgent和屏幕宽度
  const width = Math.max(document.documentElement.clientWidth, 300);
  const isMobileByWidth = width < 600;

  // 使用当前窗口大小判断设备类型（响应式切换）
  const isMobileDevice = isMobileByWidth;
  const isH5Page = route.path.startsWith("/h5");

  // 需要重定向的情况
  if ((isMobileDevice && !isH5Page) || (!isMobileDevice && isH5Page)) {
    // 使用统一的重定向逻辑处理特殊页面映射
    const { getRedirectUrl } = useDeviceRedirect();
    const routeInfo = {
      fullPath: route.fullPath,
      path: route.path,
      params: route.params,
      query: route.query,
    };

    const redirectUrl = getRedirectUrl(route.path, isMobileDevice, routeInfo);

    if (redirectUrl && redirectUrl !== route.fullPath) {
      window.location.href = redirectUrl;
    }
  }
}

// 初始化数据
await useAuthStore().nuxtServerInit();

if (process.server) {
  const res: any = await useGetNuxtConfig({
    requestUri: route.fullPath,
    abtestPage: "homepage2409",
  });

  if (res?.result?.code === 200) {
    Object.assign(pageData, res.data);
    config.public.abtestMode = "B";
    config.public.defaultCountryCode = pageData?.defaultCountryCode || "";
    config.public.userInfo = pageData.loginUser || {};
    config.public.siteId = pageData.siteId;
    config.public.siteList = pageData.siteList;

    nuxtApp.$setResponseHeaders(pageData.responseHeaders);
    let globalData = nuxtApp.$getGlobalData();
    globalData.visitCode = pageData.visitCode;
    if (pageData.headFirstScript) {
      useHead({
        script: [{ innerHTML: pageData.headFirstScript }],
      });
    }
    if (pageData.headLastScript) {
      useHead({
        script: [{ innerHTML: pageData.headLastScript }],
      });
    }
  }

  // if (isMobileView.value) {
  //   useHead({
  //     script: [
  //       {
  //         src: "/scripts/resetFontSize.js?20250220",
  //         async: false,
  //       },
  //     ],
  //   });
  // }
} else {
  // 初始化站点数据到window.siteData
  initSiteInfo(config.public.siteId, config.public.siteList || []);
}

// 防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: NodeJS.Timeout | null = null;
  return function (...args: any[]) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn(...args), delay);
  };
};

function resetFontSize() {
  const width = Math.max(document.documentElement.clientWidth, 300);
  // 如果是全屏状态，保持当前视图类型不变
  if (!isFullscreen.value) {
    const isMobilePage = width < 600;
    if (isMobileView.value !== isMobilePage) {
      // 只在非全屏状态下更新视图类型
      isMobileView.value = isMobilePage;
      checkAndRedirect();
    }
  }
  const fontSize = width >= 600 ? 16 : (80 * width) / 600;
  document.documentElement.style.fontSize = `${fontSize}px`;
}
const debouncedResetFontSize = debounce(resetFontSize, 50);

onMounted(() => {
  // 保存初始设备类型
  initialDeviceType.value = isMobile;
  checkAndRedirect();

  // 添加全屏变化监听
  document.addEventListener("fullscreenchange", handleFullscreenChange);
  document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
  document.addEventListener("mozfullscreenchange", handleFullscreenChange);
  document.addEventListener("MSFullscreenChange", handleFullscreenChange);

  window.addEventListener("resize", debouncedResetFontSize);
  window.addEventListener("orientationchange", resetFontSize);

  window?.MyStat?.addPageEvent(`system_vue_mounted`, "开始响应用户操作");
  // 指定页面增加Google reCAPTCHA 验证码
  const recaptchaPaths = [
    "/notas",
    "/h5/notas",
    "/open/notas",
    "/h5/open/notas",
  ];
  if (recaptchaPaths.includes(normalizePath(route.path))) {
    useHead({
      script: [{ src: "/scripts/recaptcha.js", async: true }],
    });
  }
  resetWhatsAppClickState();
});

onUnmounted(() => {
  // 移除全屏变化监听
  document.removeEventListener("fullscreenchange", handleFullscreenChange);
  document.removeEventListener(
    "webkitfullscreenchange",
    handleFullscreenChange
  );
  document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
  document.removeEventListener("MSFullscreenChange", handleFullscreenChange);

  window.removeEventListener("resize", debouncedResetFontSize);
  window.removeEventListener("orientationchange", resetFontSize);
});

const hiddenPaths = [
  "/notas",
  "/h5/notas",
  "/open/notas",
  "/h5/open/notas",
  "/survey",
  "/h5/survey",
  "/open",
  "/inquiry",
  "/vip",
  "/viajar-a-china",
  "/h5/comprar",
  "/h5/comprar/goods",
  "/h5/comprar/success",
];
const hideSiteSelectModal = computed(() =>
  hiddenPaths.includes(normalizePath(route.path))
);

const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: "#E50113",
  },
  Button: {
    colorPrimary: "#E50113",
    colorHoverPrimary: "#E50113",
    colorPressedPrimary: "#E50113",
    colorFocusPrimary: "#E50113",
    colorDisabledPrimary: "#E50113",
    textColorDisabledPrimary: "#E50113",
    textColorTextPrimary: "#E50113",
    textColorTextHoverPrimary: "#E50113",
    textColorTextFocusPrimary: "#E50113",
    textColorTextDisabledPrimary: "#E50113",
    textColorGhostHoverPrimary: "#E50113",
    textColorGhostPressedPrimary: "#E50113",
    borderHoverPrimary: "#E50113",
    borderPressedPrimary: "#E50113",
    borderFocusPrimary: "#E50113",
    rippleColorPrimary: "#E50113",
    textColorTextHover: "#E50113",
  },
};
</script>
