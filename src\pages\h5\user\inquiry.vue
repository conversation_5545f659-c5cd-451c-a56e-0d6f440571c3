<template>
  <div class="bg-[#F2F2F2] min-h-[100vh] pt-[1rem]">
    <div
      class="w-full border-b-1 border-solid border-gray-200 py-[0.2rem] items-center justify-center bg-white fixed top-0"
    >
      <icon-card
        color="#555"
        size="0.48rem"
        name="ep:arrow-left-bold"
        class="fixed left-[0.2rem]"
        data-spm-box="navigation-back-icon"
        @click="onBackClick"
      ></icon-card>
      <div class="text-center font-medium text-[0.36rem] leading-[0.56rem]">
        {{ authStore.i18n("cm_user.inquiry") }}
      </div>
    </div>
    <div
      class="p-[0.2rem] text-[0.28rem] leading-[0.42rem]"
      v-if="pageData.inquiryList?.length"
    >
      <div
        class="mb-[0.2rem] bg-white p-[0.2rem] rounded-[0.1rem] text-[#E50013]"
      >
        {{ authStore.i18n("cm_find_submitTip") }}
      </div>
      <n-space vertical :style="{ gap: '0.2rem 0' }">
        <div
          v-for="inquiry in pageData.inquiryList"
          :key="inquiry.id"
          class="bg-white rounded-[0.1rem]"
        >
          <div
            class="flex justify-between px-[0.2rem] py-[0.2rem] border-b !border-emerald-400 bg-green-50 rounded-t-[0.1rem] mb-[0.2rem]"
          >
            <span>{{ inquiry.goodsLookingNo }}</span>
            <span>{{ timeFormatByZone(inquiry.submitTime) }}</span>
          </div>

          <n-space vertical :style="{ gap: '0.24rem 0' }" class="px-[0.3rem]">
            <a
              :href="`/h5/goods/${sku.goodsId}${
                sku.padc ? `?padc=${sku.padc}` : ''
              }`"
              v-for="(sku, index) in inquiry?.skus"
              :key="index"
            >
              <div class="flex text-[0.26rem] text-[#000]">
                <n-image
                  lazy
                  preview-disabled
                  :src="sku.skuImage"
                  :alt="sku.goodsName"
                  class="w-[1.24rem] h-[1.24rem] flex-shrink-0 mr-[0.2rem]"
                  :img-props="{ referrerpolicy: 'no-referrer' }"
                />
                <div>
                  <div class="goodsName-ellipsis">{{ sku.goodsName }}</div>
                  <div
                    class="text-[0.24rem] text-[#7F7F7F] my-[0.10rem] leading-[0.34rem]"
                  >
                    {{ getSkuName(sku.specs) }}
                  </div>
                  <div
                    class="flex justify-between my-[0.10rem] leading-[0.28rem]"
                  >
                    <span class="text-[#E50013]">{{
                      setUnit(sku.salePrice)
                    }}</span>
                    <span>x {{ sku.buyQuantity }}</span>
                  </div>
                  <!-- 预估运费 -->
                  <div
                    class="flex justify-between leading-[0.28rem] text-[#7F7F7F]"
                  >
                    <div>{{ authStore.i18n("cm_inquiry.shippingFee") }}</div>
                    <div>
                      {{
                        sku?.estimateFreight
                          ? setUnit(sku.estimateFreight)
                          : authStore.i18n("cm_goods.pendingConfirmation")
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </n-space>

          <div class="border-t border-[#D7D7D7] p-[0.3rem] mt-[0.2rem]">
            <div class="flex justify-between">
              <span class="font-medium">{{
                authStore.i18n("cm_find_itemsCost")
              }}</span>
              <span class="text-[#e50113]">{{
                setUnit(inquiry?.subTotal)
              }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">{{
                authStore.i18n("cm_inquiry.shippingFeeTotal")
              }}</span>
              <span class="text-[#e50113]">
                {{
                  inquiry?.totalEstimateFreight
                    ? setUnit(inquiry?.totalEstimateFreight)
                    : authStore.i18n("cm_goods.pendingConfirmation")
                }}
              </span>
            </div>
          </div>
        </div>
      </n-space>
      <div>
        <img
          loading="lazy"
          v-if="pageData.isLoading"
          src="@/assets/icons/loading.svg"
          class="w-[1rem] h-[1rem] mx-auto"
          referrerpolicy="no-referrer"
        />
      </div>
    </div>
    <div v-else>
      <n-empty
        :description="authStore.i18n('cm_find.noData')"
        class="mt-[1.28rem]"
      >
      </n-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMessage } from "naive-ui";
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const router = useRouter();
const message = useMessage();
const authStore = useAuthStore();

const pageData = reactive(<any>{
  inquiryList: <any>[],
  pageInfo: <any>{
    current: 1,
    size: 10,
  },
  isLoading: false,
  noInquiryData: false,
});
onMounted(() => {
  window.addEventListener("scroll", onScrollBottom);
});
onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScrollBottom);
});

await onGoodsLookingList();
async function onGoodsLookingList(scroll?: any) {
  const res: any = await useGoodsLookingList({ page: pageData.pageInfo });
  pageData.isLoading = false;
  if (res?.result?.code === 200) {
    if (scroll && (!res.data || !res.data?.length)) {
      pageData.noInquiryData = true;
      pageData.isLoading = false;
      return;
    }
    if (scroll) {
      pageData.inquiryList = pageData.inquiryList.concat(res?.data);
    } else {
      pageData.inquiryList = res?.data;
      pageData.pageInfo = res?.page;
    }
  } else if (res?.result?.code === 403) {
    window.location.href = `/h5/user/login?pageSource=${window.location.href}`;
  } else {
    --pageData.pageInfo.current;
  }
}

// 加载下一页
async function onScrollBottom() {
  if (pageData.isLoading || pageData.noInquiryData) return;
  if (window.innerHeight + window.scrollY < document.body.scrollHeight) return; // 判断是否滚动到底部
  pageData.isLoading = true;
  pageData.pageInfo.current++;
  onGoodsLookingList("scroll");
}

function getSkuName(specs: any) {
  const names = specs?.map((spec: any) => {
    return `${spec.specName}: ${spec.itemName}`;
  });
  return names?.join("; ");
}

// 返回
function onBackClick() {
  router.go(-1);
}
</script>
<style scoped lang="scss">
.goodsName-ellipsis {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
}
</style>
