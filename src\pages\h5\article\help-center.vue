<template>
  <div class="page-wrapper">
    <mobile-search-bar></mobile-search-bar>
    <div class="center-title">
      <h1>Centro de ayuda de Chilat shop</h1>
    </div>
    <div class="center-content">
      <a href="/h5/article/frequently-questions">
        <div class="center-card">
          <icon-card
            name="wpf:faq"
            color="#db2221"
            size="40"
            class="mb-[0.24rem]"
          ></icon-card>
          <div>
            {{ authStore.i18n("cm_news.askedQuestions") }}
          </div>
        </div>
      </a>
      <a href="/h5/article?code=10002">
        <div class="center-card">
          <icon-card
            name="wpf:security-checked"
            color="#db2221"
            size="40"
            class="mb-[0.24rem]"
          ></icon-card>
          <div>
            {{ authStore.i18n("cm_news.warrantyService") }}
          </div>
        </div>
      </a>
      <a href="/h5/article/payment-methods">
        <div class="center-card">
          <icon-card
            name="mingcute:bank-card-fill"
            color="#db2221"
            size="43"
            class="mb-[0.24rem]"
          ></icon-card>
          <div>
            {{ authStore.i18n("cm_news.paymentMethods") }}
          </div>
        </div>
      </a>
    </div>
    <div class="center-contact">
      <h1>¿No encuentras las respuestas que estás buscando?</h1>
      <h2>Contacta con nosotros, aquí estamos para ayudarte.</h2>
    </div>
    <mobile-page-footer></mobile-page-footer>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();

// 设置SEO规范链接
setSeoCanonical();

// 设置SEO
useHead({
  title: `${authStore.i18n("cm_news.helpCenter")}-ChilatShop`,
});
</script>
<style scoped lang="scss">
.page-wrapper {
  height: auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
  font-size: 0.28rem;
}
.center-title {
  width: 100%;
  height: 4rem;
  display: flex;
  align-items: center;
  text-align: center;
  background-color: #f2f6f7;
  font-size: 0.52rem;
  line-height: 0.84rem;
  font-weight: 500;
  padding: 0 1rem;
}
.center-content {
  padding: 0 0.4rem;
}
.center-card {
  width: 100%;
  padding: 0.4rem;
  height: 3.6rem;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  transition: 0.4s;
  margin: 0.4rem 0;
  font-size: 0.4rem;
  line-height: 0.8rem;
  font-weight: 500;
  border-radius: 0.12rem;
  border: 0.02rem solid #eaeaea;
  box-shadow: 0 0.06rem 0.08rem -0.04rem rgba(0, 0, 0, 0.18);
  background-color: rgba(242, 246, 247, 0.*****************);

  &:hover {
    box-shadow: 0 0.12rem 0.24rem -0.04rem rgba(0, 0, 0, 0.18);
  }
}
.center-contact {
  background-color: #f6f6f8;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 1rem 0.5rem;
  h1 {
    color: #3c4043;
    font-size: 0.52rem;
    line-height: 0.8rem;
    font-weight: 500;
    margin-bottom: 0.3rem;
  }
  h2 {
    width: 6rem;
    font-size: 0.4rem;
    line-height: 0.64rem;
  }
}
</style>
