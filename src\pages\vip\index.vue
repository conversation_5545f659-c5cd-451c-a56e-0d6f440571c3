<template>
  <div class="page-container">
    <div class="w-[1280px] mx-auto px-[38px] pt-[36px] pb-[150px]">
      <div class="relative z-2">
        <div class="w-[150px] h-[36px]">
          <img
            loading="lazy"
            :src="configStore.pageTheme.chilatLogo"
            alt="logo"
            class="w-[150px]"
          />
        </div>
        <div class="flex mt-[76px]">
          <div class="mr-[42px]">
            <div
              class="w-[530px] text-[42px] leading-[50px] font-medium"
              v-html="authStore.i18n('cm_vip.personalAssistant')"
            ></div>
            <div
              class="w-[530px] text-[24px] leading-[36px] text-[#7F7F7F] mt-[48px]"
              v-html="authStore.i18n('cm_vip.findSuppliers')"
            ></div>
          </div>
          <div class="w-[633px] h-[356px] rounded-[4px] overflow-hidden">
            <video-you-tube
              :width="633"
              :height="356"
              :poster="vipCover"
              youtubeId="C09WtgdUp3M"
              titleCh="你的VIP订制找货助理"
            ></video-you-tube>
          </div>
        </div>
      </div>
    </div>
    <div class="w-[1280px] mx-auto px-[38px] pb-[150px]">
      <div class="flex justify-between flex-wrap">
        <div
          v-for="(item, index) in vipIntroData"
          :key="index"
          class="relative w-[392px] h-[384px] bg-white border border-[#333] rounded-[20px] pt-[256px] px-[27px] text-center"
        >
          <img
            loading="lazy"
            :src="item.imgUrl"
            alt="img"
            class="absolute top-0 left-0 w-full"
          />
          <div
            class="w-[54px] h-[54px] bg-[#e50113] text-[30px] leading-[54px] text-[#fff] absolute left-[50%] translate-x-[-50%] top-[185px] rounded-full border-1 border-[#fff]"
          >
            {{ index + 1 }}
          </div>
          <div class="text-[20px] leading-[24px]">
            {{ item.title }}
          </div>
          <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[12px]">
            {{ item.desc }}
          </div>
        </div>
      </div>
    </div>
    <div class="w-[1280px] mx-auto px-[38px] pb-[150px]">
      <div class="text-[34px] leading-[34px] font-medium text-center">
        {{ authStore.i18n("cm_vip.fourSteps") }}
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <div class="flex mx-auto justify-evenly mt-[110px]">
        <div
          v-for="(step, index) in vipProcData"
          :key="index"
          class="flex items-start"
        >
          <div class="w-[240px] flex flex-col items-center text-center">
            <div
              class="w-[150px] h-[150px] flex items-center justify-center overflow-hidden"
            >
              <img
                loading="lazy"
                :src="step.imgUrl"
                alt="step-image"
                class="w-full h-full object-cover"
              />
              <div
                class="text-[28px] leading-[28px] font-medium text-[#e50113] italic absolute"
              >
                {{ step.imgText }}
              </div>
            </div>
            <div class="text-[20px] leading-[24px] px-[10px] mt-[40px]">
              {{ step.title }}
            </div>
            <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[14px]">
              {{ step.desc }}
            </div>
          </div>
          <n-image
            lazy
            v-if="index < vipProcData.length - 1"
            preview-disabled
            :src="stepArrow"
            class="mt-[75px] mr-[20px]"
          ></n-image>
        </div>
      </div>
    </div>
    <div class="w-[1280px] mx-auto px-[38px] pb-[150px]">
      <div
        class="text-[42px] leading-[42px] text-center"
        v-html="authStore.i18n('cm_vip.clientsTrust')"
      ></div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[32px]"></div>
      <div
        class="text-[20px] leading-[34px] text-center mt-[18px] px-[125px] text-[#7F7F7F]"
      >
        {{ authStore.i18n("cm_vip.clientsExperience") }}
      </div>
      <div class="flex justify-between flex-wrap mt-[66px]">
        <div
          v-for="video in userVideoData"
          :key="video.id"
          class="mt-[44px] relative"
          @click="onOpenVideo(video)"
        >
          <img
            loading="lazy"
            :src="videoPlay"
            alt="video"
            class="w-[70px] h-[48px] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
          />
          <n-image
            lazy
            preview-disabled
            :src="video.poster"
            class="w-[388px] rounded-[4px]"
          />
        </div>
      </div>
    </div>
    <div class="page-footer w-[1280px] mx-auto">
      <div
        class="w-[540px] text-[42px] leading-[84px] font-semibold text-[#333] pt-[73px] pl-[40px] relative"
      >
        <span v-html="authStore.i18n('cm_nota.clickWhatsApp')"></span>
        <img
          loading="lazy"
          src="@/assets/icons/open/greenLine.svg"
          alt="line"
          class="absolute top-[139px] right-[26px]"
        />
      </div>
      <img
        loading="lazy"
        alt="click"
        class="icon"
        @click="onHandleWhatsAppClick('bottom')"
        src="@/assets/icons/open/whatsappClick.png"
      />
      <n-button
        color="#E50113"
        @click="onHandleWhatsAppClick('bottom')"
        data-spm-box="potential_user_note_submit"
        class="button"
      >
        <div
          class="flex items-center justify-center text-[34px] leading-[34px] font-semibold"
          v-html="authStore.i18n('cm_nota.clickToWhatsapp')"
        ></div>
      </n-button>
    </div>
    <div class="side-affix">
      <icon-card
        name="logos:whatsapp-icon"
        size="22px"
        title="W/app"
        :border="true"
        :multiple="true"
        @click="onHandleWhatsAppClick()"
      ></icon-card>
    </div>
  </div>
  <video-modal ref="videoModalRef"></video-modal>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";

import vipStep1 from "@/assets/icons/open/vipStep1.svg";
import vipStep2 from "@/assets/icons/open/vipStep2.svg";
import vipStep3 from "@/assets/icons/open/vipStep3.svg";
import vipStep4 from "@/assets/icons/open/vipStep4.svg";
import stepArrow from "@/assets/icons/open/stepArrow.svg";
import videoPlay from "@/assets/icons/open/videoPlay.svg";
import vipService from "@/assets/icons/open/vipService.png";
import cargoConsolidation from "@/assets/icons/open/cargoConsolidation.png";
import worryFree from "@/assets/icons/open/worryFree.png";
import vipCover from "@/assets/icons/open/vipCover.jpg";

import userVideoPoster1 from "@/assets/icons/open/userVideoPoster1.png";
import userVideoPoster2 from "@/assets/icons/open/userVideoPoster2.png";
import userVideoPoster3 from "@/assets/icons/open/userVideoPoster3.png";
import userVideoPoster4 from "@/assets/icons/open/userVideoPoster4.png";
import userVideoPoster5 from "@/assets/icons/open/userVideoPoster5.png";
import userVideoPoster6 from "@/assets/icons/open/userVideoPoster6.png";

const authStore = useAuthStore();
const configStore = useConfigStore();
const videoModalRef = ref<any>(null);

// 设置SEO规范链接
setSeoCanonical();

const vipIntroData = [
  {
    imgUrl: vipService,
    title: authStore.i18n("cm_vip.vipService"),
    desc: authStore.i18n("cm_vip.importGuidance"),
  },
  {
    imgUrl: cargoConsolidation,
    title: authStore.i18n("cm_vip.cargoConsolidation"),
    desc: authStore.i18n("cm_vip.minimumPurchase"),
  },
  {
    imgUrl: worryFree,
    title: authStore.i18n("cm_vip.worryFree"),
    desc: authStore.i18n("cm_vip.secureControl"),
  },
];

const vipProcData = [
  {
    imgUrl: vipStep1,
    imgText: authStore.i18n("cm_vip.step1"),
    title: authStore.i18n("cm_vip.productSourcing"),
    desc: authStore.i18n("cm_vip.productExpert"),
  },
  {
    imgUrl: vipStep2,
    imgText: authStore.i18n("cm_vip.step2"),
    title: authStore.i18n("cm_vip.sampleConfirmation"),
    desc: authStore.i18n("cm_vip.provideQuoteAndSamples"),
  },
  {
    imgUrl: vipStep3,
    imgText: authStore.i18n("cm_vip.step3"),
    title: authStore.i18n("cm_vip.productionTracking"),
    desc: authStore.i18n("cm_vip.orderTracking"),
  },
  {
    imgUrl: vipStep4,
    imgText: authStore.i18n("cm_vip.step4"),
    title: authStore.i18n("cm_vip.goodsReceptionShipping"),
    desc: authStore.i18n("cm_vip.receiveInspectShip"),
  },
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    poster: userVideoPoster1,
    titleCh: "1.我们的客户对我们的看法7",
  },
  {
    id: "Tj0nrnhxgXw",
    poster: userVideoPoster2,
    titleCh: "2.我们的客户对我们的看法2",
  },
  {
    id: "_omi5a-pHkA",
    poster: userVideoPoster3,
    titleCh: "3.我们的客户对我们的看法6",
  },
  {
    id: "4FVIz0PvEcE",
    poster: userVideoPoster4,
    titleCh: "4.我们的客户对我们的看法5",
  },
  {
    id: "f6Zzt70urW8",
    poster: userVideoPoster5,
    titleCh: "5.我们的客户对我们的看法1",
  },
  {
    id: "wOX-XcU4AYY",
    poster: userVideoPoster6,
    titleCh: "6.我们的客户对我们的看法9",
  },
];

const pageData = reactive(<any>{});

function onOpenVideo(video: any) {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}

async function onHandleWhatsAppClick(val?: any) {
  let clickSource = "vip页面悬浮按钮点击WhatsApp";
  if (val) {
    clickSource = "vip页面底部按钮点击WhatsApp";
  }
  window?.MyStat?.addPageEvent(
    "potential_user_click_whatsapp",
    clickSource,
    true
  );
  let paramsObj = {
    clickSource,
  };
  const res: any = await useClickWhatsapp(paramsObj);
  if (res?.result?.code === 200) {
    onWhatsAppClick();
  } else {
    showToast(res?.result?.message);
  }
}
</script>

<style scoped lang="scss">
.page-container {
  min-width: 1280px;
  height: auto;
  min-height: 100vh;
  color: #333;
}

.side-affix {
  position: fixed;
  right: 28px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
}

.page-footer {
  position: relative;
  height: 573px;
  background-size: 100%100%;
  color: #fff;
  background-image: url("@/assets/icons/open/whatsappBg.png");
  .icon {
    position: absolute;
    top: -73px;
    width: 268px;
    right: 16px;
    z-index: 1;
  }
  .button {
    position: absolute;
    top: 63px;
    right: 40px;
    height: fit-content;
    display: inline-flex;
    padding: 28px 64px;
    align-items: center;
    gap: 8px;
    border-radius: 1200px;
    border: 6px solid var(---100, #fff);
    background: #25d366;
    box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.5);
  }
}
</style>
