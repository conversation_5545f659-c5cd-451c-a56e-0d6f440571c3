/**
 * SEO规范链接工具函数
 * 用于为PC版和H5版页面添加canonical和alternate链接
 */

// 直接导出setSeoCanonical函数，无需解构调用
export function setSeoCanonical() {
  const route = useRoute();

  // 检查当前路径
  const currentPath = route.path;

  // 基础域名
  const baseUrl = "https://shop.chilat.com";

  // URL映射关系（只包含需要SEO的页面）
  const urlMap = {
    "/": "/h5",
    "/selector": "/h5/selector",
    "/blog": "/h5/blog",
    "/article": "/h5/article",
    "/article/invite": "/h5/article/invite",
    "/article/payment-methods": "/h5/article/payment-methods",
    "/article/tutorials": "/h5/article/tutorials",
    "/article/about-us": "/h5/article/about-us",
    "/article/help-center": "/h5/article/help-center",
    "/article/quick-guide": "/h5/article/quick-guide",
    "/article/frequently-questions": "/h5/article/frequently-questions",
    "/article/commission": "/h5/article/commission",
    "/tiendas-panoramicas-en-3d": "/h5/tiendas-panoramicas-en-3d",
    "/goods/looking": "/h5/search/looking",
    "/find": "/h5/find",
    "/find/submit": "/h5/find/submit",
    "/notas": "/h5/notas",
    "/open/notas": "/h5/open/notas",
    "/vip": "/h5/vip",
    "/viajar-a-china": "/h5/viajar-a-china",

    // 注意：移除了用户隐私相关页面映射
    // "/user/coupon": "/h5/user/coupon", - 用户优惠券页面
    // "/user/invite": "/h5/user/invite", - 用户邀请页面
    // "/user": "/h5/user", - 用户中心页面
    // "/user/account": "/h5/user", - 用户账户页面
    // "/user/address": "/h5/user/address", - 用户地址页面
    // "/user/inquiry": "/h5/user/inquiry", - 用户询盘页面
    // "/user/orderList": "/h5/user/orderList", - 用户订单列表
    // "/order/details": "/h5/order/details", - 订单详情页面
    // "/order/payment": "/h5/order/payment", - 支付页面
    // 注意：移除了认证相关页面映射
    // "/login": "/h5/user/login", - 登录页面
    // "/modifyPwd": "/h5/user/modifyPwd", - 修改密码页面
    // "/register": "/h5/user/register", - 注册页面
    // "/activate": "/h5/activate", - 激活页面
    // 注意：移除了感谢页/提交成功页
    // "/register/success": "/h5/user/register-success",
    // "/goods/submit-thankyou": "/h5/search/submit-thankyou",
    // "/find/submit-thankyou": "/h5/find/submit-thankyou",
    // "/notas/success": "/h5/notas/success",
  } as const;

  /**
   * 规范化路径
   */
  function normalizePathForSeo(path: string): string {
    return path.replace(/\/$/, "") || "/";
  }

  /**
   * 清理查询参数 - 移除SEO不友好的追踪参数
   */
  function cleanQueryParams(queryString: string): string {
    if (!queryString) return "";

    const params = new URLSearchParams(queryString);
    const cleanParams = new URLSearchParams();

    // 保留的SEO友好参数
    const allowedParams = [
      "code",
      "categoryId",
      "page",
      "size",
      "sort",
      "filter",
    ];

    // 移除的追踪参数和隐私参数
    const trackingParams = [
      "spm",
      "utm_source",
      "utm_medium",
      "utm_campaign",
      "utm_term",
      "utm_content",
      "fbclid",
      "gclid",
      "_ga",
      "_gid",
      // 隐私相关参数
      "email",
      "whatsapp",
      "phone",
      "mobile",
      "tel",
      "contact",
      "user",
      "userId",
      "username",
      // 用户行为追踪参数
      "firstSubmit",
      "isFirstTime",
      "visited",
      "source",
      "ref",
    ];

    for (const [key, value] of params.entries()) {
      // 只保留SEO友好的参数，移除追踪参数
      if (
        allowedParams.includes(key) ||
        !trackingParams.some((param) => key.startsWith(param))
      ) {
        // 对于code参数，确保它是有意义的内容参数
        if (key === "code" || !trackingParams.includes(key)) {
          cleanParams.set(key, value);
        }
      }
    }

    return cleanParams.toString();
  }

  /**
   * 获取当前完整URL（清理后的）
   */
  function getCurrentFullUrl(): string {
    const currentPath = normalizePathForSeo(route.path);
    const queryString = route.fullPath.includes("?")
      ? route.fullPath.split("?")[1]
      : "";
    const cleanedQuery = cleanQueryParams(queryString);
    return `${baseUrl}${currentPath}${cleanedQuery ? `?${cleanedQuery}` : ""}`;
  }

  /**
   * 获取对应的PC/H5 URL
   */
  function getCorrespondingUrl(): string | null {
    const currentPath = normalizePath(route.path);
    const queryString = route.fullPath.includes("?")
      ? route.fullPath.split("?")[1]
      : "";
    const cleanedQuery = cleanQueryParams(queryString);
    const isCurrentMobile = currentPath.startsWith("/h5/");

    if (isCurrentMobile) {
      // H5 -> PC
      const pcPath = Object.keys(urlMap).find(
        (pcUrl) => urlMap[pcUrl as keyof typeof urlMap] === currentPath
      );
      if (pcPath) {
        return `${baseUrl}${pcPath}${cleanedQuery ? `?${cleanedQuery}` : ""}`;
      }

      // 动态路由处理
      // H5商品详情页: /h5/goods/{id} -> /goods/{id}
      const h5GoodsMatch = currentPath.match(/^\/h5\/goods\/(\d+)$/);
      if (h5GoodsMatch) {
        return `${baseUrl}/goods/${h5GoodsMatch[1]}${
          cleanedQuery ? `?${cleanedQuery}` : ""
        }`;
      }

      // H5商品列表页: /h5/search/list -> /goods/list/all
      if (currentPath === "/h5/search/list") {
        const categoryId = route.query.categoryId || "all";
        return `${baseUrl}/goods/list/${categoryId}${
          cleanedQuery ? `?${cleanedQuery}` : ""
        }`;
      }

      // H5店铺页: /h5/tienda/{name} -> /tienda/{name}
      const h5TiendaMatch = currentPath.match(/^\/h5\/tienda\/([^/]+)$/);
      if (h5TiendaMatch) {
        return `${baseUrl}/tienda/${h5TiendaMatch[1]}${
          cleanedQuery ? `?${cleanedQuery}` : ""
        }`;
      }
    } else {
      // PC -> H5
      const h5Path = urlMap[currentPath as keyof typeof urlMap];
      if (h5Path) {
        return `${baseUrl}${h5Path}${cleanedQuery ? `?${cleanedQuery}` : ""}`;
      }

      // 动态路由处理
      // PC商品详情页: /goods/{id} -> /h5/goods/{id}
      const pcGoodsMatch = currentPath.match(/^\/goods\/(\d+)$/);
      if (pcGoodsMatch) {
        return `${baseUrl}/h5/goods/${pcGoodsMatch[1]}${
          cleanedQuery ? `?${cleanedQuery}` : ""
        }`;
      }

      // PC商品列表页: /goods/list/{categoryId} -> /h5/search/list
      const pcGoodsListMatch = currentPath.match(/^\/goods\/list\/(.+)$/);
      if (pcGoodsListMatch) {
        const categoryId = pcGoodsListMatch[1];
        if (categoryId === "all") {
          return `${baseUrl}/h5/search/list${
            cleanedQuery ? `?${cleanedQuery}` : ""
          }`;
        } else {
          const separator = cleanedQuery ? "&" : "?";
          return `${baseUrl}/h5/search/list${separator}categoryId=${categoryId}${
            cleanedQuery ? `&${cleanedQuery}` : ""
          }`;
        }
      }

      // PC店铺页: /tienda/{name} -> /h5/tienda/{name}
      const pcTiendaMatch = currentPath.match(/^\/tienda\/([^/]+)$/);
      if (pcTiendaMatch) {
        return `${baseUrl}/h5/tienda/${pcTiendaMatch[1]}${
          cleanedQuery ? `?${cleanedQuery}` : ""
        }`;
      }
    }

    return null;
  }

  // 设置SEO规范链接
  const normalizedPath = normalizePathForSeo(route.path);
  const isCurrentMobile = normalizedPath.startsWith("/h5/");

  // 检查当前页面是否在urlMap中有映射关系
  const hasMapping = isCurrentMobile
    ? Object.values(urlMap).includes(normalizedPath as any)
    : Object.keys(urlMap).includes(normalizedPath);

  // 检查动态路由是否有映射
  const hasDynamicMapping =
    normalizedPath.match(/^\/h5\/goods\/\d+$/) || // H5商品详情
    normalizedPath.match(/^\/goods\/\d+$/) || // PC商品详情
    normalizedPath === "/h5/search/list" || // H5商品列表
    normalizedPath.startsWith("/goods/list/") || // PC商品列表
    normalizedPath.match(/^\/h5\/tienda\/[^/]+$/) || // H5店铺页
    normalizedPath.match(/^\/tienda\/[^/]+$/); // PC店铺页

  if (!hasMapping && !hasDynamicMapping) {
    return;
  }

  const currentFullUrl = getCurrentFullUrl();
  const correspondingUrl = getCorrespondingUrl();

  if (isCurrentMobile) {
    // H5页面：添加canonical指向PC版
    if (correspondingUrl) {
      useHead({
        link: [
          {
            rel: "canonical",
            href: correspondingUrl,
          },
        ],
      });
    }
  } else {
    // PC页面：添加canonical和alternate
    const links: Array<{ rel: string; href: string; media?: string }> = [
      {
        rel: "canonical",
        href: currentFullUrl,
      },
    ];

    if (correspondingUrl) {
      links.push({
        rel: "alternate",
        media: "only screen and (max-width: 640px)",
        href: correspondingUrl,
      });
    }

    useHead({
      link: links,
    });
  }
}
