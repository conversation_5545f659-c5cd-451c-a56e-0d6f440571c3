<template>
  <div class="bg-[#FAFAFA]">
    <search-cate-card></search-cate-card>
    <div class="pb-[70px] bg-[#FAFAFA] w-[1280px] mx-auto">
      <div class="py-[56px] text-[48px] leading-[48px] font-medium">
        Preguntas frecuentes
      </div>
      <div class="flex">
        <n-space vertical>
          <div
            v-for="(column, index) in pageData.columnList"
            :key="column.id"
            class="w-[240px] cursor-pointer text-[16px] leading-[16px] py-[18px] px-[12px] rounded-[6px]"
            :class="
              pageData.selectedColumn.id === column.id
                ? 'bg-[#e50113] text-[#fff]'
                : ''
            "
            @click="onUpdateColumn(column, index)"
          >
            {{ index + 1 }}. {{ column.name }}
          </div>
        </n-space>
        <div
          class="flex-1 bg-white px-[34px] py-[20px] ml-[34px] rounded-[6px]"
        >
          <div class="text-[34px] leading-[34px] font-medium mb-[28px]">
            {{ pageData.selectedColumn.name }}
          </div>
          <n-collapse
            :show-arrow="false"
            :on-update:expanded-names="onUpdateExpandedNames"
          >
            <n-collapse-item
              v-for="(article, index) in pageData.articleList"
              :key="article.id"
              :name="article.id"
              class="p-[20px]"
              @click="onArticleClick(article, index)"
            >
              <template #header>
                <div
                  class="flex"
                  :class="{
                    'text-[#e50113]': pageData.expandedColumnIds.includes(
                      article.id
                    ),
                  }"
                >
                  <div
                    class="text-[14px] h-[22px] leading-[22px] text-[#7F7F7F] mr-[16px]"
                    :class="{
                      'text-[#e50113]': pageData.expandedColumnIds.includes(
                        article.id
                      ),
                    }"
                  >
                    0{{ index + 1 }}
                  </div>
                  <span class="text-[18px] leading-[22px]">{{
                    article.title
                  }}</span>
                </div>
              </template>
              <template #header-extra="{ collapsed }">
                <img
                  loading="lazy"
                  v-if="collapsed"
                  alt="expand"
                  class="w-[22px] ml-[60px]"
                  src="@/assets/icons/article/expand.svg"
                />
                <img
                  loading="lazy"
                  v-else
                  alt="collapse"
                  class="w-[22px] ml-[60px]"
                  src="@/assets/icons/article/collapse.svg"
                />
              </template>
              <template #arrow><span class="hidden"></span></template>
              <div
                v-html="article.content"
                data-spm-box="article-inner-link"
                class="text-[16px] leading-[28px] text-[#333] mr-[60px]"
              ></div>
            </n-collapse-item>
          </n-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import Index from "./index.vue";

const route = useRoute();
const authStore = useAuthStore();

// 设置SEO规范链接
setSeoCanonical();

const pageData = reactive(<any>{
  columnId: route?.query?.columnId || "",
  columnList: <any>[],
  selectedColumn: <any>{},
  expandedColumnIds: <any>[],
});

// 设置SEO
useHead({
  title: `${authStore.i18n("cm_news.askedQuestions")}-ChilatShop`,
});

await onGetColumnList();
async function onGetColumnList() {
  const res: any = await useListArticleCategory({
    names: ["Preguntas Frecuentes"],
  });
  if (res?.result?.code === 200) {
    pageData.columnList = res?.data[0]?.children;
    let index = 0;
    if (pageData.columnId) {
      index = pageData.columnList.findIndex(
        (column: any) => column.id === pageData.columnId
      );
    }
    pageData.selectedColumn = pageData.columnList[index];
    window?.MyStat?.addPageEvent(
      "faq_view_categories",
      `查看FAQ分类：${index + 1}. ${pageData.selectedColumn.name}`
    ); // 埋点
    onGetArticleList();
  } else {
    showToast(res.result?.message);
  }
}

async function onGetArticleList() {
  const res: any = await useListArticleByCategoryId({
    deviceType: "VISIT_DEVICE_TYPE_PC",
    articleCategoryId: pageData.selectedColumn.id,
  });
  if (res?.result?.code === 200) {
    pageData.articleList = res?.data;
  } else {
    showToast(res.result?.message);
  }
}

function onUpdateColumn(column: any, index: any) {
  const url = new URL(window.location.href);
  url.searchParams.set("columnId", column.id);
  window.history.replaceState(null, "", url.toString());
  pageData.selectedColumn = column;
  window?.MyStat?.addPageEvent(
    "faq_view_categories",
    `查看FAQ分类：${index + 1}. ${pageData.selectedColumn.name}`
  ); // 埋点
  onGetArticleList();
}

function onUpdateExpandedNames(ids: any) {
  pageData.expandedColumnIds = ids;
}

function onArticleClick(article: any, index: any) {
  if (pageData.expandedColumnIds.includes(article.id)) {
    window?.MyStat?.addPageEvent(
      "faq_view_article",
      `查看FAQ文章：${index + 1}. ${article.title}`
    );
  }
}
</script>

<style scoped lang="scss">
:deep(
    .n-collapse
      .n-collapse-item
      .n-collapse-item__content-wrapper
      .n-collapse-item__content-inner
  ) {
  padding-top: 20px;
  padding-left: 50px;
}
:deep(.n-collapse .n-collapse-item) {
  margin: 0;
}
:deep(.n-collapse .n-collapse-item .n-collapse-item__header) {
  padding: 0;
}
:deep(.n-collapse .n-collapse-item:last-child) {
  border-bottom: 1px solid rgb(239, 239, 245);
}
</style>
