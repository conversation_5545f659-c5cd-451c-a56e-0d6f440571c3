<template>
  <div v-if="pageData.showDrawer" class="product-drawer">
    <div
      class="wrapper-content h-screen flex flex-col"
      id="right-wrapper"
      data-spm-box="navigation-right-cart"
    >
      <div class="w-full relative">
        <div
          class="inline-flex py-[2px] px-[8px] h-[18px] bg-[#E50113] text-[#fff] text-[14px] leading-[14px] absolute z-20 top-[10px] right-[39px] rounded-tl-[12px] rounded-tr-[8px] rounded-br-[0px] rounded-bl-[12px]"
        >
          {{
            pageData.stat.goodsCount > 9999 ? "9999+" : pageData.stat.goodsCount
          }}
          {{ pageData.stat.goodsCount > 1 ? "ítems" : "ítem" }}
        </div>
        <img
          alt="cart"
          loading="lazy"
          src="@/assets/icons/common/product-drawer.svg"
          class="w-full bg-white relative z-10"
          referrerpolicy="no-referrer"
        />
      </div>
      <div
        :class="[
          'flex flex-col items-center bg-white relative z-2',
          { 'shadow-active': pageData.showShadow },
        ]"
      >
        <div
          v-if="
            pageData.stat?.selectTotalSalePrice ||
            pageData.stat?.selectTotalSalePrice === 0
          "
          class="text-[18px] leading-[18px] font-medium text-center mt-[14px] mb-[20px]"
        >
          <span class="text-[16px]">{{ monetaryUnit }}</span>
          {{ setNewUnit(pageData.stat?.selectTotalSalePrice, true) }}
        </div>
        <!-- <n-button
          color="#E50113"
          class="list-btn rounded-[30px] w-[176px] h-[42px] mb-[8px]"
          @click="onGoFindSubmit($event)"
        >
          <img alt="list" loading="lazy" src="@/assets/icons/common/list.svg" class="w-[24px] h-[24px] mr-[4px]" referrerpolicy="no-referrer" />
          <span class="text-[16px] leading-[16px]">{{
            authStore.i18n("cm_find.inquireNow")
          }}</span>
        </n-button> -->
        <n-button
          color="#E50113"
          class="list-btn rounded-[30px] w-[176px] h-[42px] mb-[22px]"
          @click="onFindListClick($event)"
        >
          <img
            alt="list"
            loading="lazy"
            src="@/assets/icons/common/cart-white.svg"
            class="w-[24px] h-[24px] mr-[4px]"
            referrerpolicy="no-referrer"
          />
          <span class="text-[16px] leading-[16px]">{{
            authStore.i18n("cm_find.inquireList")
          }}</span>
        </n-button>
        <!-- <n-button
          size="large"
          color="#fff"
          text-color="#000"
          class="rounded-[30px] w-[176px] h-[42px] cart-btn mb-[22px]"
          @click="onFindListClick($event)"
        >
          <img alt="cart" loading="lazy" src="@/assets/icons/common/cart.svg" class="w-[24px] h-[24px] mr-[8px]" referrerpolicy="no-referrer" />
          <span class="text-[16px] leading-[16px]">
            {{ authStore.i18n("cm_find.inquireList") }}
          </span>
        </n-button> -->
        <div class="font-medium text-[16px] leading-[16px] mb-[10px]">
          {{ authStore.i18n("cm_find.inquireGoods") }}
        </div>
        <div class="mb-[14px]">
          <n-checkbox
            v-model:checked="pageData.selectAll"
            class="select-all text-[13px] leading-[13px]"
            @update:checked="onAllSelection"
            >{{ authStore.i18n("cm_find.selectAllGoods") }} (<span
              class="text-[14px] leading-[14px] text-[#e50113]"
              >{{ selectedGoodsCount }}</span
            >/{{ pageData.stat.goodsCount }})
          </n-checkbox>
        </div>
      </div>

      <n-scrollbar class="flex-1" @scroll="onScroll">
        <div
          vertical
          class="text-center w-[220px] pb-[40px]"
          :style="{ gap: '4px 6px' }"
        >
          <div class="flex flex-wrap text-left px-[6px] gap-[4px]">
            <n-checkbox
              v-for="goods in pageData.goodsList"
              :key="goods.goodsId"
              v-model:checked="goods.selected"
              @update:checked="(value) => onGoodsSelection(value, goods)"
              class="relative group"
            >
              <img
                alt="cart"
                loading="lazy"
                src="@/assets/icons/common/view-more-btn.svg"
                class="absolute top-[68px] right-[0px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200"
                @click.stop="onOpenDetail(goods)"
                referrerpolicy="no-referrer"
              />
              <img
                lazy
                preview-disabled
                :src="goods.mainImageUrl"
                :alt="goods.goodsName"
                referrerpolicy="no-referrer"
                class="w-[100px] h-[100px] rounded-[4px]"
              />

              <div
                class="w-[100px] h-[24px] text-[12px] leading-[12px] mt-[4px] line-clamp-2"
                @click.stop="onOpenDetail(goods)"
              >
                {{ goods.goodsName }}
              </div>
            </n-checkbox>
          </div>
        </div>
      </n-scrollbar>
    </div>
  </div>
  <div
    v-if="pageData.showDrawer"
    class="drawer-placeholder shrink-0"
    id="right-placeholder"
  ></div>
  <!-- 询盘提交错误提示 -->
  <n-modal :show="pageData.errorDialogVisible" :show-icon="false">
    <n-card
      :bordered="false"
      style="
        width: 500px;
        color: #000;
        padding: 26px 0 !important;
        text-align: center;
      "
    >
      <div>
        <icon-card
          flex-col
          items-center
          size="24"
          name="mingcute:warning-line"
          class="add-btn-check"
          color="#E50113"
        ></icon-card>
        {{ pageData.errorMessage }}
      </div>
      <n-button
        size="small"
        color="#E50113"
        text-color="#fff"
        round
        @click="pageData.errorDialogVisible = false"
        class="mt-4"
      >
        {{ authStore.i18n("cm_find_shopAgain") }}</n-button
      >
    </n-card>
  </n-modal>
  <!-- 2000美元提交校验提示 -->
  <n-modal :show="pageData.submitDialogVisible" :show-icon="false">
    <n-card
      :bordered="false"
      style="
        width: 580px;
        color: #000;
        padding: 16px 10px !important;
        text-align: center;
      "
    >
      <div class="text-[18px] mb-[24px] px-[22px]">
        El importe de su producto es inferior a US$ 2000,
        <span class="text-[#e50113]"
          >los gastos de envío pueden ser más caros que el coste del
          producto</span
        >, se recomienda aumentar la compra a US$ 2000.
      </div>
      <div class="flex justify-between">
        <n-button
          round
          color="#fff"
          text-color="#000"
          @click="onConfirmSubmit($event, 'dialog')"
          data-spm-box="navigation-right-cart"
          class="border-btn w-[240px] h-[36px] text-[16px]"
        >
          {{ authStore.i18n("cm_common.buySubmit") }}</n-button
        >
        <n-button
          round
          color="#E50113"
          text-color="#fff"
          @click="onCancelSubmit"
          class="w-[240px] h-[36px] text-[16px]"
        >
          {{ authStore.i18n("cm_common.buyAgain") }}</n-button
        >
      </div>
    </n-card>
  </n-modal>
  <ClientOnly>
    <goods-detail
      :visible="pageData.visible"
      :goods="pageData.currentGoods"
      @onCloseDetail="onCloseDetail"
      @onUpdateList="onGetCart"
    ></goods-detail>
  </ClientOnly>

  <SubmitLoadingModal :show="pageData.submitLoading" />
</template>
<script setup lang="ts" name="ProductDrawer">
import SubmitLoadingModal from "@/pages/find/components/SubmitLoadingModal.vue";

const router = useRouter();
import { useAuthStore } from "@/stores/authStore";
const emit = defineEmits(["updateDrawer"]);
const message = useMessage();
const authStore = useAuthStore();
const loginRegister = inject<any>("loginRegister");
const pageData = reactive({
  showDrawer: false,
  goodsList: <any>[],
  stat: <any>{},
  errorDialogVisible: false,
  errorMessage: "",
  submitDialogVisible: false,
  submitLoading: false,
  selectAll: false,
  visible: false,
  currentGoods: <any>{},
  showShadow: false,
});

const selectedGoodsCount = computed(() => {
  // 统计已选中的商品数量
  return pageData.goodsList.filter((goods: any) => goods.selected).length;
});

watch(
  () => authStore.cartList,
  (newList: any, oldList: any) => {
    if (newList?.length) {
      updateCartState({
        goodsList: newList,
        stat: authStore.cartStat,
      });
      if (oldList?.length === 0) {
        pageData.showDrawer = true;
        emit("updateDrawer", true);
      }
    } else {
      pageData.goodsList = [];
      pageData.showDrawer = false;
      emit("updateDrawer", false);
    }
  },
  { immediate: true }
);

onGetCart("init");
// 数据获取和同步
async function onGetCart(type?: any) {
  const res: any = await useGetCart({});
  if (res?.result?.code === 200) {
    updateCartState(res?.data, type);
    authStore.getCartList(res?.data);
  }
}

function updateCartState(cartData: any, initType?: string) {
  const hasItems = cartData.goodsList?.length > 0;

  pageData.goodsList = cartData.goodsList.map((goods: any) => ({
    ...goods,
    selected: goods.skuList.some((sku: any) => sku.selected),
  }));

  pageData.stat = cartData.stat;
  pageData.selectAll = pageData.goodsList.every((goods: any) => goods.selected);

  if (initType === "init") {
    pageData.showDrawer = hasItems;
    emit("updateDrawer", hasItems);
  }
}

async function onAllSelection(value: any) {
  onUpdateCart({ selectedAll: value });
}

function onGoodsSelection(value: boolean, goods: any) {
  onUpdateCart({ selected: value, goodsId: goods.goodsId, padc: goods.padc });
}

async function onUpdateCart(params: any) {
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    onGetCart();
  } else {
    message.error(res?.result?.message, {
      duration: 3000,
    });
  }
}

function onGoFindSubmit(event: any) {
  window?.MyStat?.addPageEvent("click_start_looking", `点击创建询盘按钮`, true); // 埋点
  if (pageData.stat?.selectTotalSalePrice < 2000) {
    pageData.submitDialogVisible = true;
    window?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_open",
      `未满2000美元对话框-打开`
    ); // 埋点
    return;
  }
  onConfirmSubmit(event);
}

async function onConfirmSubmit(event?: any, from?: any) {
  pageData.submitLoading = true;
  if (from) {
    window?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_ingore",
      `未满2000美元对话框-忽略`
    ); // 埋点
  }
  window?.MyStat?.addPageEvent(
    "right_click_inquiry",
    `侧边栏点击进入询盘提交页`
  ); // 埋点

  const selectedSkuList = <any>[];
  pageData.goodsList?.forEach((goods: any) => {
    goods.skuList.forEach((sku: any) => {
      if (sku.selected) {
        selectedSkuList.push({
          quantity: sku.buyQty,
          skuId: sku.skuId,
          spm: sku.spm,
          routeId: goods.routeId,
          padc: sku.padc,
        });
      }
    });
  });
  if (!!window?.fbq) {
    window?.fbq("track", "InitiateCheckout", {
      currency: "USD",
      num_items: selectedSkuList?.length,
      contents: selectedSkuList,
    });
  }
  if (!!window?.ttq) {
    window?.ttq?.track("InitiateCheckout", {
      currency: "USD",
      value: pageData.stat?.selectTotalSalePrice,
      content_type: "product",
      description: JSON.stringify(selectedSkuList),
    });
  }
  const res: any = await useGetInquiry({
    params: selectedSkuList,
    siteId: window?.siteData?.siteInfo?.id,
  });
  if (res?.result?.code === 200) {
    const inquiryInfo = res?.data;
    await authStore.setInquiryInfo(inquiryInfo);
    navigateToPage(`/find/submit`, {}, false, event);
  } else if (res?.result?.code === 403) {
    loginRegister?.openLogin();
  } else {
    window?.MyStat?.addPageEvent(
      "submit_start_looking_error",
      `进入询盘信息页错误：${res?.result?.message}`
    ); // 埋点
    pageData.errorDialogVisible = true;
    setTimeout(() => {
      pageData.errorDialogVisible = false;
    }, 3000);
    pageData.errorMessage =
      res?.result?.message || authStore.i18n("cm_find.errorMessage");
  }
  pageData.submitLoading = false;
}

function onCancelSubmit() {
  window?.MyStat?.addPageEvent(
    "less_then_2000usd_dialog_close",
    `未满2000美元对话框-关闭`
  ); // 埋点
  pageData.submitDialogVisible = false;
}

function onFindListClick(event: any) {
  window?.MyStat?.addPageEvent("right_click_carrito", `侧边栏点击进入购物车`); // 埋点
  navigateToPage(`/find`, {}, true, event);
}

async function onOpenDetail(goods: any) {
  pageData.visible = true;
  pageData.currentGoods = goods;
}

function onCloseDetail() {
  pageData.visible = false;
}

function onScroll(e: Event) {
  const target = e.target as HTMLElement;
  pageData.showShadow = target.scrollTop > 0;
}
</script>

<style scoped lang="scss">
.left-placeholder {
  height: 100vh;
}
.wrapper-content {
  position: fixed;
  top: 0px;
  bottom: 0;
  right: -1000px; //fix 首次渲染时 侧边栏一闪而过的问题
  left: unset;
  background: #fff;
  border-left: 1px solid #efeff5;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}
.product-drawer {
  width: 220px;
  position: relative;
  box-sizing: border-box;
  z-index: 99;
  flex-shrink: 0;
  .drawer-header {
    width: 100%;
    height: auto;
    font-size: 16px;
    text-align: center;
    padding: 20px 10px;
    color: #fff;
    background-image: linear-gradient(90deg, #e58c64, #d67669);
  }
}
.list-btn.n-button:hover {
  background: #f20114;
}

.cart-btn {
  border: 1px solid #a6a6a6;
  &:hover {
    color: #e50113;
    border: 1px solid #e50113;
    img {
      content: url("@/assets/icons/common/cart-active.svg");
    }
  }
}
.border-btn {
  border: 1px solid #a6a6a6;
}
.border-btn.n-button:hover {
  background: #e50113;
  color: #fff;
  border: none;
}
:deep(.n-checkbox .n-checkbox-box-wrapper) {
  width: 0;
}

:deep(.n-checkbox .n-checkbox-box) {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  left: 4px;
  top: 14px;
}
:deep(.select-all.n-checkbox .n-checkbox-box) {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  left: -16px;
  top: 50%;
  transform: translateY(-50%);
}
:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
}
:deep(.n-checkbox__label) {
  padding: 0;
}
.shadow-active {
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.25);
}
</style>
