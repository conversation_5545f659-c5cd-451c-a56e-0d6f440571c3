<template>
  <div
    class="list-wrapper relative"
    :class="{ 'my-6': !!(props?.images && props?.images.length >= 7) }"
  >
    <!--carouselOne 是为了修改n-carousel下只有一个item的时候 item会局底部的问题  -->
    <n-carousel
      class="static"
      :show-arrow="!!(props?.images && props?.images.length >= 7)"
      :show-dots="false"
      slides-per-view="auto"
      :space-between="9"
      :loop="false"
      direction="vertical"
      :class="{ carouselOne: !!(props?.images && props?.images.length === 1) }"
    >
      <n-carousel-item v-for="(image, index) in props?.images" :key="index">
        <div class="relative">
          <n-image
            lazy
            preview-disabled
            :style="{
              width: props.imgWidth,
              height: props.imgHeight,
            }"
            :src="image"
            :alt="props.goodsName"
            object-fit="fill"
            @click="onImageSelect(index)"
            class="container"
            :class="{ selected: selectedNum === index }"
            :img-props="{ referrerpolicy: 'no-referrer' }"
          >
          </n-image>
          <icon-card
            size="26"
            color="#F2F2F2"
            name="solar:play-bold"
            class="absolute"
            style="top: 50%; left: 50%; transform: translate(-50%, -50%)"
            v-if="props.showVideoIcon && index == 0"
          />
        </div>
      </n-carousel-item>
      <template #arrow="{ prev, next }">
        <div class="custom-arrow">
          <div class="custom-arrow-icon custom-arrow--left">
            <icon-card
              color="#AAA"
              name="ri:arrow-up-s-line"
              size="30"
              @click="prev"
            />
          </div>
          <div class="custom-arrow-icon custom-arrow--right">
            <icon-card
              color="#AAA"
              name="ri:arrow-down-s-line"
              size="30"
              @click="next"
            />
          </div>
        </div>
      </template>
    </n-carousel>
  </div>
</template>

<script setup lang="ts" name="ImageCarousel">
const emit = defineEmits(["selectEvent"]);
const props = defineProps({
  images: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  imgWidth: {
    type: String,
    default: "60px",
  },
  imgHeight: {
    type: String,
    default: "60px",
  },
  imgSelected: {
    type: Number,
    default: 0,
  },
  showVideoIcon: {
    type: Boolean,
    default: false,
  },
  goodsName: {
    type: String,
    default: "",
  },
});

const selectedNum = ref(props.imgSelected);
watch(
  () => props.imgSelected,
  (newVal: any) => {
    selectedNum.value = newVal;
  }
);

function onImageSelect(index: number) {
  selectedNum.value = index;
  emit("selectEvent", index);
}
</script>
<style scoped lang="scss">
.custom-arrow {
  .custom-arrow-icon {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f7f7f7;
    width: 100%;
    height: 20px;
  }
  .custom-arrow--left {
    top: -24px;
  }
  .custom-arrow--right {
    bottom: -24px;
  }
}
:deep(.carouselOne .n-carousel__slides) {
  transform: none !important;
}
:deep(.n-carousel__slide) {
  width: auto !important;
  height: auto !important;
}
.list-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
}

:deep(.n-image) {
  background: "#F7F7F7";
  cursor: pointer;
}
.container {
  object-fit: cover; /* 指定图片如何适应容器，cover表示保持纵横比并填充整个容器 */
  touch-action: manipulation; /* 禁用双击缩放 */
  -webkit-touch-callout: none; /* 禁用长按调出菜单 */
}

.container:hover {
  box-shadow: inset 0 0 0 24px #c3c4c5;
}

.selected {
  border: 1px solid red !important;
}
</style>
