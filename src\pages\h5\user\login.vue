<template>
  <div class="bg-[#8f0000] min-h-100vh pb-[1rem]">
    <div
      class="flex h-[0.96rem] border-b-1 border-solid border-gray-200 flex justify-center items-center bg-white"
    >
      <a
        class="flex justify-center items-center"
        href="/h5"
        data-spm-box="navigation-logo-icon"
      >
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          :src="pageTheme.logo"
          class="w-[3rem] mx-auto"
        />
      </a>
    </div>
    <div class="login-wrapper" :style="`background-image: url(${loginBg})`">
      <div class="bg-white py-[0.3rem] rounded-[0.16rem]">
        <div
          class="text-center font-medium my-[0.3rem] text-[0.4rem] leading-[0.56rem]"
        >
          {{ authStore.i18n("cm_common_login") }}
        </div>
        <div>
          <!-- 登录 -->
          <n-form
            :rules="rules"
            ref="loginFromRef"
            :model="pageData.loginForm"
            class="px-[0.4rem]"
          >
            <n-form-item
              path="username"
              :label="authStore.i18n('cm_common.username')"
            >
              <n-input
                v-trim
                clearable
                class="h-[0.96rem]"
                @keydown.enter.prevent
                @blur="onBlurEvent(pageData.loginForm.username, 'email')"
                v-model:value="pageData.loginForm.username"
                :placeholder="authStore.i18n('cm_common.inputEmail')"
              />
            </n-form-item>
            <n-form-item
              path="password"
              :label="authStore.i18n('cm_common.password')"
            >
              <n-input
                v-trim
                clearable
                class="h-[0.96rem]"
                @keydown.enter.prevent
                @blur="onBlurEvent(pageData.loginForm.password, 'password')"
                :type="pageData.showPwd ? 'text' : 'password'"
                v-model:value="pageData.loginForm.password"
                :placeholder="authStore.i18n('cm_common.inputPassword')"
              >
                <template #suffix>
                  <icon-card
                    size="24"
                    color="#7F7F7F"
                    class="cursor-pointer"
                    :name="
                      pageData.showPwd
                        ? 'weui:eyes-on-outlined'
                        : 'weui:eyes-off-outlined'
                    "
                    @click="pageData.showPwd = !pageData.showPwd"
                  /> </template
              ></n-input>
            </n-form-item>
            <div class="flex justify-end mt-[0.2rem]">
              <div
                class="text-[#034AA6] cursor-pointer text-[0.28rem] leading-[0.4rem]"
                @click="onGoModifyPwd"
              >
                {{ authStore.i18n("cm_common.forgotMyPwd") }}
              </div>
            </div>
            <n-form-item>
              <n-button
                size="large"
                color="#E50113"
                text-color="#fff"
                class="rounded-[0.16rem] px-[0.16rem] w-full h-[0.96rem] text-[0.32rem] leading-[0.4rem]"
                @click="onLogin(pageData.loginForm)"
              >
                {{ authStore.i18n("cm_common_login") }}
              </n-button>
            </n-form-item>
            <div class="flex justify-end text-[0.28rem]">
              {{ authStore.i18n("cm_common_registerTip") }}
              <div
                class="text-[#034AA6] cursor-pointer text-[0.32rem] leading-[0.4rem] ml-[0.1rem]"
                @click="onGoRegister"
              >
                {{ authStore.i18n("cm_common_register") }}
              </div>
            </div>
          </n-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";

import loginBg from "@/assets/icons/loginBg.jpg";

const route = useRoute();
const authStore = useAuthStore();

// 设置SEO规范链接
setSeoCanonical();

authStore.setShowAnchor(false);
authStore.setShowCarousel(false);
const loginFromRef = ref<FormInst | null>(null);
const pageTheme = computed(() => useConfigStore().getPageTheme);
const pageData = reactive({
  showPwd: false,
  loginForm: <any>{
    username: "",
    password: "",
  },
});

const rules: FormRules = {
  username: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const pattern = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputEmailTips"));
      } else if (!pattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_email_format_error",
          `邮箱格式错误，邮箱：${value}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.emailTips"));
      }
      return true;
    },
  },
  password: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const specialCharPattern = /[^A-Za-z\d]/; // 特殊字符匹配
      const pattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$/; // 8-16位包含数字及字母
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputPwdTips"));
      }
      // 校验是否包含特殊字符
      else if (specialCharPattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_password_format_error",
          `密码格式错误：${authStore.i18n("cm_common.pwdFormatTips")}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.pwdFormatTips"));
      }
      // 校验8-16位包含数字及字母
      else if (!pattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_password_format_error",
          `密码格式错误：${authStore.i18n("cm_common.pwdLengthTips")}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.pwdLengthTips"));
      }
      return true;
    },
  },
};

onMounted(() => {
  // 记录未登录首页是否打开过注册登录弹框
  if (route.query.pageSource === "/h5" || route.query.pageSource === "/h5/") {
    window?.MyStat.setSessionValue("isClickLoginModal", "true");
  }
  let loginInfo = sessionStorage.getItem("loginInfo");
  if (loginInfo) {
    const info = JSON.parse(loginInfo);
    pageData.loginForm.username = info.username;
    pageData.loginForm.password = info.password;
    sessionStorage.removeItem("loginInfo");
  }
});

// 去注册
function onGoRegister() {
  const loginInfo = {
    username: pageData.loginForm.username,
    password: pageData.loginForm.password,
  };
  sessionStorage.setItem("loginInfo", JSON.stringify(loginInfo));
  window?.MyStat?.addPageEvent("passport_switch_register", "切换到注册TAB");
  navigateToPage("/h5/user/register", route.query, false);
}

// 去修改密码
function onGoModifyPwd() {
  const loginInfo = {
    username: pageData.loginForm.username,
    password: pageData.loginForm.password,
  };
  sessionStorage.setItem("loginInfo", JSON.stringify(loginInfo));
  window?.MyStat?.addPageEvent("passport_switch_forgot", "切换到忘记密码TAB");
  navigateToPage("/h5/user/modifyPwd", route.query, false);
}

// 登录
async function onLogin(params: any) {
  await loginFromRef.value?.validate((errors: any) => {
    errors?.length &&
      window?.MyStat?.addPageEvent(
        "passport_login_verify_fail",
        `登录表单校验不通过：${errors.map((item: any) =>
          item.map((it: any) => it.message)
        )}`
      );
  });
  try {
    const res: any = await useLogin(params);
    if (res?.result?.code === 200) {
      await authStore.setUserInfo(res?.data);
      await onAddCart();
      if (!!window?.gtag) {
        window?.gtag("event", "LoginSuccess");
      }
      window.location.replace(route.query?.pageSource || "/h5");
    } else {
      showToast(res?.result?.message);
      window?.MyStat?.addPageEvent(
        "passport_login_submit_error",
        `登录提交错误：${res.result?.message}`
      );
    }
  } catch (error) {
    showToast(error);
  }
}

// 加购
async function onAddCart() {
  if (!route.query.goods) return;
  const res: any = await useAddCart(JSON.parse(route.query.goods));
  if (res.result?.code === 200) {
    showToast(
      authStore.i18n("cm_goods.addToList"),
      1500,
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/11/df87d5e1-099d-48ca-97a4-239bb000748a.png"
    );
    authStore.getCartList();
  } else {
    showToast(res.result?.message);
  }
}

function onBlurEvent(val: any, type: string) {
  if (!val) return;
  const eventMap: { [key: string]: { event: string; message: string } } = {
    email: {
      event: "passport_input_email",
      message: "在账号窗口，输入了邮箱",
    },
    password: {
      event: "passport_input_password",
      message: "在账号窗口，输入了密码",
    },
    invite: {
      event: "passport_input_invite",
      message: "在账号窗口，输入了邀请码",
    },
    captcha: {
      event: "passport_input_verify_code",
      message: "在账号窗口，输入了验证码",
    },
  };

  const eventInfo = eventMap[type];
  if (eventInfo) {
    window?.MyStat?.addPageEvent(eventInfo.event, eventInfo.message);
  }
}
</script>

<style scoped lang="scss">
:deep(.n-input__input-el) {
  height: 0.96rem;
}
:deep(.n-form-item-label) {
  font-size: 0.28rem;
  line-height: 0.4rem;
}
.login-wrapper {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: 100% 11rem;
  padding: 2.8rem 0.4rem 0;
}
:deep(.n-input__placeholder) {
  font-size: 0.28rem;
}
</style>
