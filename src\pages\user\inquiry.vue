<template>
  <div class="flex-1 bg-white h-full rounded py-4">
    <div class="text-xl font-medium px-5 mb-2">
      {{ authStore.i18n("cm_inquiry.myInquiry") }}
    </div>
    <div v-if="pageData.inquiryList.length" class="h-[95%] flex flex-col">
      <n-scrollbar class="overflow-auto flex-1">
        <n-table :bordered="false" :single-line="true">
          <thead>
            <tr>
              <th>{{ authStore.i18n("cm_inquiry.product") }}</th>
              <th class="w-[160px]">
                {{ authStore.i18n("cm_inquiry.specifications") }}
              </th>
              <th class="w-[80px]">
                {{ authStore.i18n("cm_inquiry.count") }}
              </th>
              <th class="w-[60px]">
                {{ authStore.i18n("cm_inquiry.unit") }}
              </th>
              <th class="w-[110px] !whitespace-normal">
                {{ authStore.i18n("cm_inquiry.unitPrice") }}
                <span class="text-[12px] break-normal whitespace-nowrap">
                  ({{ monetaryUnit }})
                </span>
              </th>
              <th class="w-[110px] !whitespace-normal">
                {{ authStore.i18n("cm_find_itemsCost") }}
                <span class="text-[12px] break-normal whitespace-nowrap">
                  ({{ monetaryUnit }})
                </span>
              </th>
              <th class="w-[120px] !whitespace-normal">
                {{ authStore.i18n("cm_inquiry.shippingFee") }}
                <span class="text-[12px] break-normal whitespace-nowrap">
                  ({{ monetaryUnit }})
                </span>
              </th>
              <th></th>
            </tr>
          </thead>
          <tbody
            v-for="inquiry in pageData.inquiryList"
            :key="inquiry.id"
            class="text-[13px]"
          >
            <tr>
              <td colspan="8" class="!p-0 !border-none">
                <div
                  class="border-t-0 border-b h-[50px] leading-[50px] !border-emerald-400 bg-green-50 text-sm px-4"
                >
                  <div class="flex text-[#333]">
                    <div class="mr-12">
                      <span class="text-[#666]">{{
                        authStore.i18n("cm_inquiry.goodsLookingNo")
                      }}</span
                      >{{ inquiry.goodsLookingNo }}
                    </div>
                    <div>
                      <span class="text-[#666]">{{
                        authStore.i18n("cm_inquiry.submitTime")
                      }}</span
                      >{{ timeFormatByZone(inquiry.submitTime) }}
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr v-for="(sku, index) in inquiry?.skus" :key="index">
              <!-- 商品名称 -->
              <td>
                <a
                  target="_blank"
                  :href="`/goods/${sku.goodsId}${
                    sku.padc ? `?padc=${sku.padc}` : ''
                  }`"
                  class="flex items-center flex-nowrap"
                >
                  <div class="w-[62px] h-[62px] flex-shrink-0">
                    <n-image
                      lazy
                      preview-disabled
                      :src="sku.skuImage"
                      :alt="sku.goodsName"
                      style="height: 100%; width: 100%"
                      :img-props="{ referrerpolicy: 'no-referrer' }"
                    />
                  </div>
                  <div
                    class="pl-2 text-current hover:text-[#e50113] hover:cursor-pointer"
                  >
                    <n-ellipsis :line-clamp="4" :tooltip="false">{{
                      sku.goodsName
                    }}</n-ellipsis>
                  </div>
                </a>
              </td>
              <!-- 商品规格 -->
              <td>
                <div v-for="(spec, specIndex) in sku.specs" :key="specIndex">
                  <div>{{ spec.specName }}:{{ spec.itemName }}</div>
                </div>
              </td>
              <!-- 数量 -->
              <td>{{ sku.buyQuantity }}</td>
              <!-- 价格单位 -->
              <td>{{ sku.priceUnitName }}</td>
              <!-- 销售价 -->
              <td>{{ sku.salePrice }}</td>
              <!-- 销售总价 -->
              <td>{{ sku.totalSalePrice }}</td>
              <!-- 预估运费 -->
              <td :class="{ 'text-[#e50113]': sku?.estimateFreight }">
                {{
                  sku?.estimateFreight
                    ? sku.estimateFreight
                    : authStore.i18n("cm_goods.pendingConfirmation")
                }}
              </td>
              <td
                v-if="index === 0"
                :rowspan="inquiry.skus.length"
                class="!border-l !border-[#efeff5ff] w-[140px]"
              >
                {{ authStore.i18n("cm_find_submitTip") }}
              </td>
            </tr>
          </tbody>
        </n-table>
      </n-scrollbar>
      <n-pagination
        show-size-picker
        show-quick-jumper
        :page-sizes="[10, 20, 30]"
        :item-count="pageData.pageInfo.total"
        v-model:page="pageData.pageInfo.current"
        v-model:page-size="pageData.pageInfo.size"
        :on-update:page="onUpdatePageNo"
        :on-update:page-size="onUpdatePageSize"
        class="mt-3 text-center flex justify-center"
      >
        <template #prefix="{ itemCount }">
          {{ authStore.i18n("cm_inquiry.total") }}
          {{ itemCount }}
        </template>
        <template #goto>{{ authStore.i18n("cm_inquiry.jumpTo") }}</template>
      </n-pagination>
    </div>
    <n-empty
      v-else
      class="mt-[20vh]"
      :description="authStore.i18n('cm_find.noData')"
    >
    </n-empty>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const pageData = reactive(<any>{
  inquiryList: <any>[],
  pageInfo: <any>{
    current: 1,
    size: 10,
  },
});

useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});

onGoodsLookingList();
async function onGoodsLookingList() {
  const res: any = await useGoodsLookingList({ page: pageData.pageInfo });
  if (res?.result?.code === 200) {
    pageData.inquiryList = res?.data;
    pageData.pageInfo = res?.page;
  } else if (res?.result?.code === 403) {
    window.location.href = "/";
  }
}

function onUpdatePageNo(page: number) {
  pageData.pageInfo.current = page;
  onGoodsLookingList();
}
function onUpdatePageSize(pageSize: number) {
  pageData.pageInfo.current = 1;
  pageData.pageInfo.size = pageSize;
  onGoodsLookingList();
}
</script>
<style scoped lang="scss">
thead tr th {
  background: #fff;
  font-weight: 500;
  padding: 8px 10px;
  line-height: 18px;
}
</style>
