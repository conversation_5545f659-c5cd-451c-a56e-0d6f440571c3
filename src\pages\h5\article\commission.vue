<template>
  <div class="page-wrapper">
    <mobile-search-bar></mobile-search-bar>
    <div class="header_banner">
      <div class="text-center">
        <n-space vertical :style="{ gap: '0.32rem 0' }">
          <div class="title">COMISIONES DE CHILAT SHOP</div>
          <div class="sub-content mb-[0.3rem]">
            AUMENTA EL VOLUMEN DE COMPRA Y PAGA MENOS COMISIÓN
          </div>
          <div class="content">
            <n-space vertical :style="{ gap: '0.4rem 0' }">
              <div>
                Cobraremos una comisión basada en el valor total de los
                productos del pedido.
              </div>
              <div>
                La tasa de comisión se determina en función del valor de los
                productos que haya comprado. <br />
                (Valor de la mercancía = Cantidad * Precio unitario).
              </div>
              <div>
                Por ejemplo, si compra artículos con un valor de pedido único
                superior a 30,000 $, la tasa de comisión de su pedido será del
                5%. (Comisión = valor de compra * tasa de comisión)
              </div>
              <div class="text-[#8f8f8f]">
                Consulte la tabla siguiente para ver el porcentaje
                correspondiente al valor de los bienes adquiridos:
              </div>
              <div>
                <table>
                  <thead>
                    <tr class="bg-[#e50113] text-[#fff]">
                      <td>Nivel</td>
                      <td>Tasa de comisión</td>
                      <td>Valor de la compra (USD)</td>
                    </tr>
                  </thead>

                  <tbody>
                    <tr v-for="(com, index) in commissionData" :key="index">
                      <td>
                        <n-image lazy preview-disabled :src="com.img" />
                      </td>
                      <td>
                        {{ com.percent }}
                      </td>
                      <td>
                        {{ com.money }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </n-space>
          </div>
        </n-space>
      </div>
    </div>
    <mobile-page-footer></mobile-page-footer>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();

// 设置SEO规范链接
setSeoCanonical();

// 设置SEO
useHead({
  title: `${authStore.i18n("cm_news.commission")}-ChilatShop`,
});

const commissionData = [
  {
    img: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/19/5b7521b2-9d25-4fbc-bc99-21d0f082999c.png",
    percent: "5%",
    money: "+$30,000",
  },
  {
    img: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/19/f3281b61-2e52-4105-a203-7a85d7e77c89.png",
    percent: "6%",
    money: "+$20,000",
  },
  {
    img: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/19/a43b29e6-f248-44d1-934f-4196ce43ea2c.png",
    percent: "7%",
    money: "+$10,000",
  },
  {
    img: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/19/8d1edeb4-dd70-49bf-8c1e-59a650e80ac6.png",
    percent: "8%",
    money: "+$0",
  },
];
</script>

<style scoped lang="scss">
.page-wrapper {
  height: auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
  font-size: 0.28rem;
}
.header_banner {
  width: 100%;
  position: relative;
  padding: 0.32rem 0.2rem 1rem;
  text-align: center;
  .title {
    font-size: 0.46rem;
    line-height: 0.54rem;
    // font-weight: 500;
  }
  .sub-content {
    font-size: 0.35rem;
    line-height: 0.45rem;
    font-weight: 500;
  }
  .content {
    text-align: left;
  }
}
table {
  width: 100%;
  border-spacing: 0 0.16rem;
}
thead {
  tr td {
    padding: 0.28rem 0.12rem;
    white-space: nowrap;
  }
}

tbody {
  tr td {
    padding: 0.16rem 0.12rem;
    font-size: 0.32rem;
    text-align: center;
  }
}

tbody tr:nth-child(even) {
  background-color: #dfdbdc;
}

tr td:first-child {
  border-top-left-radius: 0.12rem;
  border-bottom-left-radius: 0.12rem;
}

tr td:last-child {
  border-top-right-radius: 0.12rem;
  border-bottom-right-radius: 0.12rem;
}
</style>
