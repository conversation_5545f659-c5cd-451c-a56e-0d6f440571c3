<template>
  <div
    class="mobile-container"
    :class="!!pageData.from ? 'pb-[2rem]' : 'pb-[0.6rem]'"
  >
    <!-- 头部信息 -->
    <mobile-search-bar :showCart="true"></mobile-search-bar>
    <div v-if="route?.query?.keyword" class="mt-[0.4rem] px-[0.4rem]">
      <img
        :alt="authStore.i18n('cm_goods.customSearch')"
        class="w-[2.76rem] mx-auto"
        src="@/assets/icons/common/no-data.svg"
      />
      <div class="text-[0.28rem] leading-[0.42rem] text-[#7F7F7F] mt-[0.24rem]">
        <span>{{ authStore.i18n("cm_search.tipsPartOne") }}&nbsp;</span>
        <span class="inline-flex items-start text-[#11263B]">
          "
          <span
            class="font-medium max-w-[2.8rem] overflow-hidden text-ellipsis whitespace-nowrap inline-block"
          >
            {{ pageData.goodsItems[0].goodsName }}
          </span>
          "
        </span>
        <span>&nbsp;{{ authStore.i18n("cm_search.productNotFound") }}</span>
        <span> &nbsp;{{ authStore.i18n("cm_search.suggestedOptions") }} </span>
      </div>

      <ul
        class="list-disc ml-[0.4rem] text-[0.28rem] leading-[0.42rem] mt-[0.04rem] text-[#7F7F7F]"
      >
        <li>{{ authStore.i18n("cm_search.tipsPartThree") }}</li>
        <li>{{ authStore.i18n("cm_search.tipsPartFour") }}</li>
        <li>{{ authStore.i18n("cm_search.tipsPartFive") }}</li>
      </ul>
      <div class="mt-[0.44rem] overflow-hidden">
        <div
          class="font-medium text-[0.52rem] leading-[0.52rem] text-[#11263B]"
        >
          {{ authStore.i18n("cm_goods.customSearch") }}
        </div>
        <div class="mt-[0.24rem]">
          <span class="text-[0.28rem] leading-[0.42rem] text-[#7F7F7F]">{{
            authStore.i18n("cm_search.tipsPartSix")
          }}</span>
          <span
            class="font-medium text-[0.32rem] leading-[0.54rem] text-[#11263B]"
            >&nbsp;{{ authStore.i18n("cm_search.tipsPartSeven") }}</span
          >
        </div>
        <img
          :alt="authStore.i18n('cm_goods.customSearch')"
          class="w-[2.24rem] h-[0.92rem] object-cover object-top mt-[-0.2rem] mr-[1.2rem] ml-auto"
          src="@/assets/icons/common/find-form.svg"
        />
      </div>
    </div>
    <div v-else class="mt-[0.56rem] px-[0.4rem]">
      <div class="font-medium text-[1.04rem] leading-[1.16rem] text-[#11263B]">
        {{ authStore.i18n("cm_goods.customSearch") }}
      </div>
      <div class="mt-[0.56rem] text-[0.32rem] leading-[0.48rem] text-[#7F7F7F]">
        {{ authStore.i18n("cm_search.tipsPartSix") }}
        <span class="font-medium text-[0.36rem] leading-[0.6rem] text-[#11263B]"
          >{{ authStore.i18n("cm_search.tipsPartSeven") }}
        </span>
      </div>
      <img
        :alt="authStore.i18n('cm_goods.customSearch')"
        class="w-[2.24rem] h-[0.92rem] object-cover object-top mr-[1.2rem] ml-auto"
        src="@/assets/icons/common/find-form.svg"
      />
    </div>

    <n-form
      ref="lookingFormRef"
      :model="pageData"
      :rules="rules"
      class="pb-[0.4rem] px-[0.4rem]"
    >
      <n-form-item path="name" :label="authStore.i18n('cm_submit.username')">
        <n-input
          v-trim
          @keydown.enter.prevent
          v-model:value="pageData.name"
          :placeholder="authStore.i18n('cm_submit.usernamePlaceholder')"
          @blur="
            onBlurEvent(pageData.name, authStore.i18n('cm_submit.username'))
          "
        />
      </n-form-item>
      <n-form-item path="email" :label="authStore.i18n('cm_search.email')">
        <n-input
          v-trim
          @keydown.enter.prevent
          v-model:value="pageData.email"
          :placeholder="authStore.i18n('cm_search.pleaseInputEmail')"
          @blur="onBlurEvent(pageData.email, authStore.i18n('cm_search.email'))"
        />
      </n-form-item>
      <n-form-item
        path="countryId"
        :label="authStore.i18n('cm_submit.country')"
      >
        <n-select
          filterable
          value-field="id"
          label-field="countryEsName"
          :options="pageData.countryList"
          v-model:value="pageData.countryId"
          @update:value="
            (value, option) =>
              onSelectCountry(
                value,
                option,
                authStore.i18n('cm_submit.country')
              )
          "
          :placeholder="authStore.i18n('cm_submit.countryPlaceholder')"
        />
      </n-form-item>
      <n-form-item :span="10" path="whatsapp" label="Whatsapp">
        <n-input-group>
          <n-input
            v-trim
            readonly
            class="!w-[1.92rem]"
            @keydown.enter.prevent
            v-model:value="pageData.areaCode"
            :placeholder="authStore.i18n('cm_submit.telephonePrefix')"
          />
          <n-input
            v-trim
            clearable
            @keydown.enter.prevent
            v-model:value="pageData.whatsapp"
            :placeholder="authStore.i18n('cm_search.pleaseInputWhatsapp')"
            @blur="onBlurEvent(pageData.whatsapp, 'Whatsapp')"
          />
        </n-input-group>
      </n-form-item>
      <!-- 商品信息标题 -->
      <div class="mb-[0.24rem]">
        <span>{{ authStore.i18n("cm_search.productInfo") }}</span>
        <div class="text-[0.24rem] text-gray-500 ml-[0.16rem]">
          ({{ authStore.i18n("cm_search.expandable") }})
        </div>
      </div>

      <!-- 商品信息部分 - 循环渲染多个商品 -->
      <div
        v-for="(item, index) in pageData.goodsItems"
        :key="index"
        class="mb-[0.48rem] pt-[0.36rem] border border-gray-200 p-[0.32rem] rounded-[0.16rem] relative"
      >
        <!-- 删除按钮 -->
        <div
          class="absolute top-[0.16rem] right-[0.2rem] z-10 select-none cursor-pointer"
        >
          <icon-card
            size="24"
            color="#e50113"
            v-if="pageData.goodsItems.length > 1"
            @click="removeGoodsItem(index)"
            name="material-symbols:delete-outline"
          />
        </div>

        <!-- 商品名称 -->
        <n-form-item
          :path="`goodsItems[${index}].goodsName`"
          :label="authStore.i18n('cm_search.goodsName')"
        >
          <n-input
            v-trim
            @keydown.enter.prevent
            v-model:value="item.goodsName"
            :placeholder="authStore.i18n('cm_search.pleaseInputGoodsName')"
            @blur="
              onBlurEvent(item.goodsName, authStore.i18n('cm_search.goodsName'))
            "
          />
        </n-form-item>

        <!-- 商品图片 -->
        <n-form-item
          :path="`goodsItems[${index}].imageList`"
          :label="authStore.i18n('cm_search.goodsImages')"
        >
          <file-upload
            :max="5"
            :multiple="true"
            :preview="true"
            @uploadEvent="(val) => onFileUpload(val, index)"
            :files="item.imageList"
            spmCode="goods_find_upload_image"
          />
          <div class="text-[0.24rem] text-gray-500 mt-[0.08rem]">
            {{ authStore.i18n("cm_search.limitedCapacity") }}
          </div>
        </n-form-item>

        <!-- 商品数量 -->
        <n-form-item
          :path="`goodsItems[${index}].count`"
          :label="authStore.i18n('cm_search.quantity')"
        >
          <n-input-number
            class="w-full"
            :min="1"
            :step="1"
            :precision="0"
            :max="10000000"
            @keydown.enter.prevent
            v-model:value="item.count"
            :placeholder="authStore.i18n('cm_search.pleaseQuantity')"
            @blur="
              onBlurEvent(item.count, authStore.i18n('cm_search.quantity'))
            "
          />
        </n-form-item>

        <!-- 价格限制 -->
        <n-form-item :label="authStore.i18n('cm_search.searchPriceLimit')">
          <n-radio-group
            v-model:value="item.isPriceLimited"
            :name="`radiogroup-${index}`"
          >
            <n-space vertical>
              <n-radio :value="true">{{
                authStore.i18n("cm_search.priceUnlimit")
              }}</n-radio>
              <n-row>
                <n-radio :value="false" class="mr-[0.32rem]"></n-radio>
                <n-space vertical>
                  <n-input-number
                    :min="0.01"
                    :max="99999999.99"
                    :precision="2"
                    size="medium"
                    clearable
                    v-model:value="item.minPrice"
                    :disabled="item.isPriceLimited"
                    :placeholder="authStore.i18n('cm_search.minPrice')"
                    @blur="
                      onBlurEvent(
                        item.minPrice,
                        authStore.i18n('cm_search.minPrice')
                      )
                    "
                  />
                  <n-input-number
                    :min="0.01"
                    :max="99999999.99"
                    :precision="2"
                    size="medium"
                    clearable
                    v-model:value="item.maxPrice"
                    :disabled="item.isPriceLimited"
                    :placeholder="authStore.i18n('cm_search.maxPrice')"
                    @blur="
                      onBlurEvent(
                        item.maxPrice,
                        authStore.i18n('cm_search.maxPrice')
                      )
                    "
                  />
                </n-space>
              </n-row>
            </n-space>
          </n-radio-group>
        </n-form-item>
      </div>

      <!-- 添加商品按钮 -->
      <div class="mb-[0.48rem] flex justify-center">
        <div
          v-if="pageData.goodsItems.length < 10"
          class="flex items-center gap-[0.16rem] px-[0.32rem] py-[0.16rem] border-2 border-dashed border-[#034AA6] text-[#034AA6] cursor-pointer rounded-[0.16rem] select-none"
          @click="addGoodsItem"
        >
          <span class="text-[0.4rem]">+</span>
          <span class="text-[0.28rem]">{{
            authStore.i18n("cm_search.addProduct")
          }}</span>
          <span class="text-[0.24rem] text-gray-500"
            >({{ pageData.goodsItems.length }}/10)</span
          >
        </div>
        <div
          v-else
          class="flex items-center gap-[0.16rem] px-[0.32rem] py-[0.16rem] border-2 border-dashed border-gray-300 text-gray-400 rounded-[0.16rem] flex justify-center flex-wrap gap-y-[0.06rem]"
        >
          <span class="text-[0.4rem]">+</span>
          <span class="text-[0.28rem] leading-[0.28rem] flex-shrink-0">{{
            authStore.i18n("cm_search.addProduct")
          }}</span>
          <span class="text-[0.24rem] leading-[0.24rem] flex-shrink-0">
            ({{ authStore.i18n("cm_search.maxResultsReached") }} 10/10)
          </span>
        </div>
      </div>
      <n-form-item
        path="description"
        :label="authStore.i18n('cm_search.searchDescription')"
      >
        <n-input
          v-trim
          type="textarea"
          :autosize="{
            minRows: 3,
            maxRows: 5,
          }"
          @keydown.enter.prevent
          v-model:value="pageData.description"
          :placeholder="authStore.i18n('cm_search.pleaseInputGoodsDescription')"
          @blur="
            onBlurEvent(
              pageData.description,
              authStore.i18n('cm_search.searchDescription')
            )
          "
        />
      </n-form-item>
      <n-form-item path="isAcceptSimilarProduct">
        <n-checkbox
          v-model:checked="pageData.isAcceptSimilarProduct"
          label="Productos similares aceptados"
        />
      </n-form-item>
    </n-form>
    <div
      class="page-footer flex justify-center h-[1.28rem]"
      v-if="!!pageData.from"
    >
      <div
        class="flex flex-col items-center -mt-[0.28rem]"
        @click="onCloseConfirm"
      >
        <div class="w-[1.04rem] h-[1.04rem] rounded-full center-icon">
          <div
            class="w-[0.76rem] h-[0.76rem] mx-auto rounded-full bg-[#11263B] mt-[0.08rem] flex items-center justify-center relative z-10"
          >
            <img
              alt="close"
              class="w-[0.36rem] h-[0.36rem] rotate-[-45deg]"
              src="@/assets/icons/common/mobile-find.svg"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 固定在底部的提交按钮 -->
    <div
      class="fixed-submit-container"
      :class="
        !!pageData.from ? 'bottom-[1.26rem] !pb-[0.4rem]' : 'bottom-[0rem]'
      "
    >
      <div @click="onSubmitGoodsLooking" class="submit-btn cursor-pointer">
        {{ authStore.i18n("cm_search.submitLooking") }}
      </div>
    </div>
  </div>
  <n-modal :show="pageData.dialogVisible" :show-icon="false">
    <div
      class="w-[6.7rem] bg-white rounded-[0.08rem] text-center pt-[0.6rem] px-[0.24rem] pb-[0.24rem]"
    >
      <div class="text-[0.32rem] leading-[0.32rem] px-[0.4rem]">
        {{ authStore.i18n("cm_search.exitWithoutSaving") }}
      </div>
      <div class="flex justify-between mt-[0.6rem]">
        <n-button
          round
          color="#fff"
          text-color="#000"
          @click="onCloseCustomSearch"
          class="w-[2.9rem] h-[0.8rem] text-[0.32rem] border-solid border-1 border-[#11263B]"
        >
          {{ authStore.i18n("cm_search.confirmExit") }}</n-button
        >
        <n-button
          round
          color="#11263B"
          text-color="#fff"
          @click="pageData.dialogVisible = false"
          class="w-[2.9rem] h-[0.8rem] text-[0.32rem]"
        >
          {{ authStore.i18n("cm_search.keepEditing") }}</n-button
        >
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts" name="GoodsLooking">
import { useAuthStore } from "@/stores/authStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";
import { getSiteInfo } from "@/utils/siteUtils";

const route = useRoute();
const router = useRouter();
const message = useMessage();
const authStore = useAuthStore();
const config = useRuntimeConfig();

// 设置SEO规范链接
setSeoCanonical();

const userInfo = ref<any>({});
userInfo.value = config.public.userInfo;
const siteInfo = computed(() => getSiteInfo());

// 创建默认商品项的函数
function createDefaultGoodsItem() {
  return {
    goodsName: "",
    imageList: [],
    count: null,
    isPriceLimited: true,
    minPrice: null,
    maxPrice: null,
  };
}

const pageData = reactive<any>({
  name: "",
  email: "",
  whatsapp: "",
  description: "",
  goodsItems: [
    {
      goodsName: route?.query?.keyword || route?.query?.cateName || "",
      imageList: [],
      count: null,
      isPriceLimited: true,
      minPrice: null,
      maxPrice: null,
    },
  ],
  isAcceptSimilarProduct: true,
  isSubmitLoading: false,
  areaCode: null,
  countryId: null,
  countryList: <any>[],
  countryRegexes: <any>{},
  from: route?.query?.from || "",
  dialogVisible: false,
});

// 添加商品项方法
function addGoodsItem() {
  if (pageData.goodsItems.length >= 10) {
    message.warning(authStore.i18n("cm_search.expandable"), {
      duration: 3000,
    });
    return;
  }

  pageData.goodsItems.push(createDefaultGoodsItem());
  window?.MyStat?.addPageEvent(
    "goods_find_click_add_goods",
    `点击按钮添加第${pageData.goodsItems.length}个找货商品`
  );
  updateValidationRules();
}

// 删除商品项方法
function removeGoodsItem(index: any) {
  if (pageData.goodsItems.length > 1) {
    pageData.goodsItems.splice(index, 1);
    window?.MyStat?.addPageEvent(
      "goods_find_click_remove_goods",
      `点击按钮删除第${index + 1}个找货商品`
    );
    updateValidationRules();
  }
}

// 动态更新表单验证规则
function updateValidationRules() {
  // 清除旧的商品验证规则
  Object.keys(rules).forEach((key: any) => {
    if (key.startsWith("goodsItems[")) {
      delete rules[key];
    }
  });

  // 为每个商品项添加验证规则
  pageData.goodsItems.forEach((_: any, index: any) => {
    rules[`goodsItems[${index}].goodsName`] = {
      required: true,
      trigger: "blur",
      message: authStore.i18n("cm_search.goodsNameTips"),
    };
    rules[`goodsItems[${index}].imageList`] = {
      required: true,
      trigger: "blur",
      message: authStore.i18n("cm_search.goodsImagesTips"),
      validator(rule: FormItemRule, value: any) {
        return value?.length > 0;
      },
    };
    rules[`goodsItems[${index}].count`] = {
      required: true,
      trigger: "blur",
      message: authStore.i18n("cm_search.pleaseQuantity"),
      validator(rule: FormItemRule, value: any) {
        return value > 0 ? true : false;
      },
    };
  });
}

const lookingFormRef = ref<FormInst | null>(null);
const rules: FormRules = {
  name: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_submit.usernamePlaceholder"),
  },
  email: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_search.emailTips"),
    validator(rule: FormItemRule, value: any) {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return pattern.test(value);
    },
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_submit.countryPlaceholder"),
  },
  whatsapp: {
    required: true,
    trigger: "blur",
    message: (() => {
      const phoneCountMessage = pageData.countryRegexes?.phoneCount
        ? `${authStore.i18n("cm_submit.whatsappTips")} ${
            pageData.countryRegexes?.phoneCount
          } ${authStore.i18n("cm_submit.whatsapp")}`
        : authStore.i18n("cm_submit.whatsappRequired");
      return `${phoneCountMessage}`;
    })(),
    validator(rule: FormItemRule, value: any) {
      const lengths =
        pageData.countryRegexes.phoneCount &&
        pageData.countryRegexes.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            return true;
          }
        }
      } else {
        if (value) {
          return true;
        }
      }
      return false;
    },
  },
  // 修改为动态路径验证
  "goodsItems[0].goodsName": {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_search.goodsNameTips"),
  },
  "goodsItems[0].imageList": {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_search.goodsImagesTips"),
    validator(rule: FormItemRule, value: any) {
      return value?.length > 0;
    },
  },
  "goodsItems[0].count": {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_search.pleaseQuantity"),
    validator(rule: FormItemRule, value: any) {
      return value > 0 ? true : false;
    },
  },
  "goodsItems[0].description": {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_search.descriptionTips"),
  },
};

onBeforeMount(() => {
  document.title = authStore.i18n("cm_common.documentTitle");

  // 处理以图搜图跳转过来的图片
  if (route?.query?.imageUrl) {
    const imageUrl = route.query.imageUrl as string;
    // 将图片URL转换为FileUpload组件需要的格式
    pageData.goodsItems[0].imageList.push({
      fileName: `search-image-${Date.now()}.jpg`,
      fileUrl: imageUrl,
    });
  }
});

onMounted(async () => {
  if (isEmptyObject(userInfo.value)) {
    navigateToPage("/h5/user/login", { pageSource: route.fullPath }, false);
  }
  await onGetCountry();
  await onGetContactInfo();

  if (siteInfo.value.id && !pageData.countryId) {
    const defaultCountry = pageData.countryList.find(
      (country: any) => country.id == siteInfo.value.id
    );
    if (defaultCountry) {
      setCountryData(defaultCountry);
    }
  }
  if (!pageData.email) {
    pageData.email = userInfo.value.username;
  }

  // 初始化验证规则
  updateValidationRules();
});

async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
  }
}

// 获取联系人信息
async function onGetContactInfo() {
  try {
    const res: any = await useGetContactInfo({});
    if (res?.result?.code === 200 && res?.data) {
      const contactInfo = res.data;

      if (contactInfo.name) {
        pageData.name = contactInfo.name;
      }

      if (contactInfo.email) {
        pageData.email = contactInfo.email;
      }

      if (contactInfo.whatsapp) {
        pageData.whatsapp = contactInfo.whatsapp;
      }

      if (contactInfo.countryId && pageData.countryList?.length > 0) {
        const selectedCountry = pageData.countryList.find(
          (country: any) => country.id === contactInfo.countryId
        );
        if (selectedCountry) {
          setCountryData(selectedCountry);
        }
      }
    }
  } catch (error) {
    console.log(error);
  }
}

function setCountryData(country: any) {
  pageData.countryId = country.id;
  pageData.areaCode = country.areaCode;
  pageData.countryRegexes = country;
  if (pageData.countryRegexes?.phoneCount) {
    (rules["whatsapp"] as any).message = `${authStore.i18n(
      "cm_submit.whatsappTips"
    )} ${pageData.countryRegexes.phoneCount} ${authStore.i18n(
      "cm_submit.whatsapp"
    )}`;
  }
}

function onSelectCountry(_value: any, country: any, label: any) {
  setCountryData(country);
  // 如果没有长度校验，设置必填提示
  if (!pageData.countryRegexes?.phoneCount) {
    (rules["whatsapp"] as any).message = authStore.i18n(
      "cm_submit.whatsappRequired"
    );
  }
  window?.MyStat?.addPageEvent(
    "goods_find_select",
    `${label} 选择：${country?.countryEsName}`
  );
}

// 修改文件上传方法，添加索引参数
function onFileUpload(val: any, index: number) {
  pageData.goodsItems[index].imageList = val;
}

// 西班牙语序数词
function getSpanishOrdinal(num: number): string {
  const ordinals = [
    "primer",
    "segundo",
    "tercer",
    "cuarto",
    "quinto",
    "sexto",
    "séptimo",
    "octavo",
    "noveno",
    "décimo",
  ];
  return ordinals[num - 1] || `${num}°`;
}

async function onSubmitGoodsLooking() {
  window?.MyStat?.addPageEvent("goods_find_submit", `找货信息表单提交`);
  try {
    await lookingFormRef.value?.validate();
  } catch (validationErrors) {
    // 处理验证错误
    const errorMessages: string[] = [];

    if (validationErrors && Array.isArray(validationErrors)) {
      validationErrors.forEach((error: any) => {
        const trueField = error[0];
        if (trueField.field === "name") {
          errorMessages.push(trueField.message);
        } else if (trueField.field === "email") {
          errorMessages.push(trueField.message);
        } else if (trueField.field === "whatsapp") {
          errorMessages.push(trueField.message);
        } else if (trueField.field === "countryId") {
          errorMessages.push(trueField.message);
        } else if (trueField.field && trueField.field.includes("goodsName")) {
          const index = trueField.field.match(/\[(\d+)\]/)?.[1];
          const ordinal = getSpanishOrdinal(parseInt(index) + 1);
          errorMessages.push(
            `Por favor, escribe el nombre del ${ordinal} producto`
          );
        } else if (trueField.field && trueField.field.includes("imageList")) {
          const index = trueField.field.match(/\[(\d+)\]/)?.[1];
          const ordinal = getSpanishOrdinal(parseInt(index) + 1);
          errorMessages.push(
            `Por favor, sube la imagen del ${ordinal} producto`
          );
        } else if (trueField.field && trueField.field.includes("count")) {
          const index = trueField.field.match(/\[(\d+)\]/)?.[1];
          const ordinal = getSpanishOrdinal(parseInt(index) + 1);
          errorMessages.push(
            `Por favor, indica la cantidad del ${ordinal} producto`
          );
        }
      });
    }
    // 智能显示错误提示
    if (errorMessages.length > 0) {
      if (errorMessages.length <= 3) {
        // 错误较少时，显示具体错误
        showToast(errorMessages.join(", "));
        window?.MyStat?.addPageEvent(
          "goods_find_submit_error",
          `找货信息表单提交错误：${errorMessages.join(", ")}`
        );
      } else {
        // 错误较多时，显示前3个错误 + 通用提示
        const firstThreeErrors = errorMessages.slice(0, 3).join(", ");
        showToast(
          `${firstThreeErrors}, etc. Por favor, verifica si el formulario está completo.`
        );
        window?.MyStat?.addPageEvent(
          "goods_find_submit_error",
          `找货信息表单提交错误：${firstThreeErrors}, etc. Por favor, verifica si el formulario está completo.`
        );
      }
    }
    return;
  }

  if (pageData.isSubmitLoading) return;

  let params = {
    name: pageData.name?.trim(),
    email: pageData.email?.trim(),
    whatsapp: pageData.whatsapp?.trim(),
    areaCode: pageData.areaCode,
    countryId: pageData.countryId,
    isAcceptSimilarProduct: pageData.isAcceptSimilarProduct,
    remark: pageData.description?.trim() || "",
    goodsFindLineList: pageData.goodsItems.map((item: any) => ({
      goodsName: item.goodsName?.trim(),
      imageList: (item.imageList || [])
        .map((img: any) => (typeof img === "string" ? img : img.fileUrl))
        .filter((url: string) => url),
      count: item.count,
      isPriceLimited: !item.isPriceLimited,
      minPrice:
        !item.isPriceLimited && item.minPrice ? Number(item.minPrice) : null,
      maxPrice:
        !item.isPriceLimited && item.maxPrice ? Number(item.maxPrice) : null,
    })),
  };

  pageData.isSubmitLoading = true;
  try {
    const res: any = await useSubmitGoodsFind(params);
    if (res?.result?.code === 200) {
      window?.MyStat?.addPageEvent(
        "goods_find_submit_success",
        `保存找货信息成功，顺序号：${res?.data?.goodsFindNo}`,
        true
      );
      pageData.isSubmitLoading = false;
      window.location.href = `/h5/search/submit-thankyou`;
    } else {
      window?.MyStat?.addPageEvent(
        "goods_find_submit_error",
        `找货信息表单错误：${res?.result?.message}`
      );
      message.error(
        res?.result?.message || authStore.i18n("cm_find.errorMessage"),
        {
          duration: 3000,
        }
      );
      pageData.isSubmitLoading = false;
    }
  } catch (error) {
    pageData.isSubmitLoading = false;
  }
}

function onCloseConfirm() {
  pageData.dialogVisible = true;
}

function onCloseCustomSearch() {
  pageData.dialogVisible = false;
  router.replace({ path: pageData.from });
}

// 输入框埋点事件
async function onBlurEvent(value: string, label: any) {
  window?.MyStat?.addPageEvent("goods_find_input", `${label} 输入：${value}`);
}
</script>

<style scoped lang="scss">
.mobile-container {
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  font-size: 0.28rem;
  line-height: 0.4rem;
}
.fixed-submit-container {
  position: fixed;
  left: 0;
  right: 0;
  padding: 0.24rem 0.16rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(0.5rem);
  z-index: 2;
}

.submit-btn {
  width: 100%;
  height: 0.88rem;
  line-height: 0.88rem;
  color: #fff;
  border-radius: 0.24rem;
  background: #e50113;
  text-align: center;
  font-size: 0.32rem;
  font-weight: 500;
}

.page-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(0.5rem);
  z-index: 100;
  .center-icon {
    position: relative;
    overflow: hidden;
    z-index: -1;
  }
  .center-icon::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0.32rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(0.5rem);
    border-radius: 199.98rem;
  }
}
:deep(.n-form-item .n-form-item-blank) {
  display: block;
}
</style>
