<template>
  <div class="mobile-container">
    <!-- 头部信息 -->
    <mobile-search-bar></mobile-search-bar>
    <div
      class="w-full flex-1 flex flex-col overflow-hidden px-[0.24rem] pb-[0.48rem]"
    >
      <div
        class="text-[0.48rem] leading-[0.48rem] medium border-b-1 border-[#7F7F7F] pb-[0.2rem] my-[0.36rem] text-center"
      >
        {{ authStore.i18n("cm_blog_blogTitle") }}
      </div>
      <div class="pb-[0.4rem]">
        <div class="min-h-[40vh]">
          <a
            v-for="(article, index) in pageData.articleList"
            :key="article.id"
            :href="`/h5/article?code=${article.articleCode}`"
            :class="{
              'pb-[0.2rem] mb-[0.2rem] border-b border-[#F2F2F2]':
                index !== pageData.articleList.length - 1,
            }"
            class="flex"
            data-spm-box="blog-article-list"
            :data-spm-index="index + 1"
          >
            <div
              class="flex-shrink-0 w-[2rem] h-[1.32rem] mr-[0.16rem] rounded-[0.04rem] flex justify-center items-center"
            >
              <n-image
                lazy
                preview-disabled
                :src="article?.logo"
                v-if="article?.logo"
                class="w-full h-full"
              >
              </n-image>
              <n-image
                lazy
                v-else
                preview-disabled
                :src="configStore.pageTheme.logo"
                class="w-[1.68rem]"
              ></n-image>
            </div>
            <n-ellipsis
              :line-clamp="3"
              :tooltip="false"
              class="text-[0.28rem] leading-[0.32rem] font-medium h-[fit-content]"
            >
              {{ article.title }}
            </n-ellipsis>
          </a>
        </div>
      </div>
      <div>
        <img
          loading="lazy"
          v-if="pageData.isLoading"
          src="@/assets/icons/loading.svg"
          class="w-[1rem] h-[1rem] mx-auto"
        />
      </div>
      <div
        v-if="!pageData.isLoading && pageData.noMoreData"
        class="text-[#7F7F7F] text-center text-[0.28rem] leading-[0.28rem]"
      >
        {{ authStore.i18n("cm_blog_noMoreData") }}
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
const authStore = useAuthStore();
const configStore = useConfigStore();

// 设置SEO规范链接
setSeoCanonical();

const pageData = reactive(<any>{
  articleList: <any>[],
  pageInfo: <any>{
    current: 1,
    size: 10,
    total: 0,
  },
  isLoading: false,
  noMoreData: false,
});

// 设置SEO
useHead({
  title: "Chilat shop blog-Chilat Shop",
  meta: [
    {
      name: "description",
      content:
        "El blog de Chilat Shop explica cómo utilizar el mercado mayorista en línea de Chilat para que importar desde China sea más conveniente.",
    },
  ],
});

onMounted(() => {
  window.addEventListener("scroll", onScrollBottom);
});
onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScrollBottom);
});

onGetBlogList();
async function onGetBlogList(scroll?: any) {
  const res: any = await useGetBlogList({
    page: pageData.pageInfo,
    deviceType: "VISIT_DEVICE_TYPE_H5",
  });
  pageData.isLoading = false;
  if (res?.result?.code === 200) {
    if (scroll && (!res.data || !res.data?.length)) {
      pageData.noMoreData = true;
      return;
    }
    if (scroll) {
      pageData.articleList = pageData.articleList.concat(res?.data);
    } else {
      pageData.articleList = res?.data;
    }
    pageData.pageInfo = res?.page;
  } else {
    showToast(res.result?.message);
  }
}

// 加载下一页
async function onScrollBottom() {
  if (pageData.isLoading || pageData.noMoreData) return;
  if (window.innerHeight + window.scrollY < document.body.scrollHeight) return; // 判断是否滚动到底部
  pageData.isLoading = true;
  pageData.pageInfo.current++;
  onGetBlogList("scroll");
}
</script>

<style scoped lang="scss"></style>
