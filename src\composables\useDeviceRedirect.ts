/**
 * 设备重定向 Composable
 * 统一管理PC/H5页面重定向逻辑，避免代码重复
 *
 * 设备检测策略：
 * 1. 屏幕宽度
 * 2. UserAgent
 */

// 特殊页面重定向映射（只配置路径结构变化的页面）
const SPECIAL_PC_TO_H5_MAP = {
  "/goods/looking": "/h5/search/looking",
  "/goods/submit-thankyou": "/h5/search/submit-thankyou",
  "/login": "/h5/user/login",
  "/modifyPwd": "/h5/user/modifyPwd",
  "/register": "/h5/user/register",
  "/register/success": "/h5/user/register-success",
  "/user/account": "/h5/user",
} as const;

// 特殊页面H5到PC的反向映射
const SPECIAL_H5_TO_PC_MAP = Object.fromEntries(
  Object.entries(SPECIAL_PC_TO_H5_MAP).map(([pc, h5]) => [h5, pc])
) as Record<string, string>;

// 不需要重定向的页面
const NO_REDIRECT_PATHS = new Set<string>([
  "/inquiry",
  "/looking",
  "/activate",
  "/h5/comprar",
  "/h5/comprar/goods",
  "/h5/comprar/success",
]);

/**
 * 路由信息接口
 */
interface RouteInfo {
  fullPath: string;
  path: string;
  params?: Record<string, any>;
  query?: Record<string, any>;
}

function buildQueryString(query?: Record<string, any>): string {
  if (!query || Object.keys(query).length === 0) {
    return "";
  }
  return "?" + new URLSearchParams(query as Record<string, string>).toString();
}

/**
 * 获取 H5 重定向 URL
 */
export function getH5RedirectUrl(
  currentPath: string,
  routeInfo: RouteInfo
): string {
  // 检查是否在排除列表中
  if (NO_REDIRECT_PATHS.has(currentPath)) {
    return "";
  }

  // 如果已经是H5页面，不需要重定向
  if (currentPath.startsWith("/h5")) {
    return "";
  }

  // 商品详情页重定向
  const isGoodsDetailPage = /^\/goods\/\d+$/.test(currentPath);
  if (isGoodsDetailPage) {
    return `/h5${routeInfo.fullPath}`;
  }

  // 商品列表页面特殊处理
  const isPcGoodsListPage = /^\/goods\/list/.test(currentPath);
  if (isPcGoodsListPage) {
    const categoryId = routeInfo.params?.id;
    if (categoryId !== "all") {
      let mobilePath = routeInfo.fullPath.replace(
        `/goods/list/${categoryId}`,
        "/h5/search/list"
      );
      const querySymbol = mobilePath.includes("?") ? "&" : "?";
      if (!routeInfo.query?.categoryId) {
        mobilePath = mobilePath.concat(
          `${querySymbol}categoryId=${categoryId}`
        );
      }
      return mobilePath;
    } else {
      return routeInfo.fullPath.replace(`/goods/list/all`, "/h5/search/list");
    }
  }

  // Tienda 页面重定向
  const isTiendaPage = /^\/tienda\/[^/]+$/.test(currentPath);
  if (isTiendaPage) {
    return `/h5${routeInfo.fullPath}`;
  }

  // 检查特殊页面映射（只有路径结构变化的页面）
  const specialMobileURL =
    SPECIAL_PC_TO_H5_MAP[currentPath as keyof typeof SPECIAL_PC_TO_H5_MAP];
  if (specialMobileURL) {
    return `${specialMobileURL}${buildQueryString(routeInfo.query)}`;
  }

  // 默认规则：PC页面 -> H5页面（直接添加 /h5 前缀）
  // 适用于所有可以直接转换的页面，如：
  // /article -> /h5/article, /blog -> /h5/blog, /vip -> /h5/vip 等
  return `/h5${routeInfo.fullPath}`;
}

/**
 * 获取 PC 重定向 URL
 */
export function getPCRedirectUrl(
  currentPath: string,
  routeInfo: RouteInfo
): string {
  // 检查是否在排除列表中
  if (NO_REDIRECT_PATHS.has(currentPath)) {
    return "";
  }

  // 如果不是H5页面，不需要重定向
  if (!currentPath.startsWith("/h5")) {
    return "";
  }

  // 商品详情页重定向
  const isGoodsDetailPageMobile = /^\/h5\/goods\/\d+$/.test(currentPath);
  if (isGoodsDetailPageMobile) {
    return routeInfo.fullPath.replace("/h5", "");
  }

  // 商品列表页面特殊处理
  const isMobileGoodsListPage = /^\/h5\/search\/list/.test(currentPath);
  if (isMobileGoodsListPage) {
    const categoryId = routeInfo.query?.categoryId || "all";
    return routeInfo.fullPath.replace(
      "/h5/search/list",
      `/goods/list/${categoryId}`
    );
  }

  // Tienda 页面重定向
  const isTiendaPageMobile = /^\/h5\/tienda\/[^/]+$/.test(currentPath);
  if (isTiendaPageMobile) {
    return routeInfo.fullPath.replace(/^\/h5/, "");
  }

  // 检查特殊页面映射（只有路径结构变化的页面）
  const specialPcURL = SPECIAL_H5_TO_PC_MAP[currentPath];
  if (specialPcURL) {
    return `${specialPcURL}${buildQueryString(routeInfo.query)}`;
  }

  // 默认规则：H5页面 -> PC页面（去除 /h5 前缀）
  // 适用于所有可以直接转换的页面，如：
  // /h5/article -> /article, /h5/blog -> /blog, /h5/vip -> /vip 等
  return routeInfo.fullPath.replace(/^\/h5/, "") || "/";
}

/**
 * 获取重定向URL（统一入口）
 */
export function getRedirectUrl(
  currentPath: string,
  isMobileView: boolean,
  routeInfo: RouteInfo
): string {
  if (isMobileView) {
    // PC -> H5 重定向
    return getH5RedirectUrl(currentPath, routeInfo);
  } else {
    // H5 -> PC 重定向
    return getPCRedirectUrl(currentPath, routeInfo);
  }
}

/**
 * 设备重定向 Composable
 */
export function useDeviceRedirect() {
  return {
    getH5RedirectUrl,
    getPCRedirectUrl,
    getRedirectUrl,
  };
}
